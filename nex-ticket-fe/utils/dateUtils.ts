export function formatDateToDate(date: Date, timezone: string | undefined = undefined): string {
  // From a date returns a string with the format "Day, DD MMM YYYY"
  const formatter = new Intl.DateTimeFormat(undefined, {
    weekday: 'short',
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    timeZone: timezone,
  })
  const parts = formatter.formatToParts(date).filter(part => part.type !== 'literal')

  const weekday = parts[0].value
  const day = parts[1].value
  const month = parts[2].value
  const year = parts[3].value

  return `${weekday}, ${day} ${month} ${year}`
}

export function formatDateToTime(date: Date, timezone: string | undefined = undefined): string {
  // From a date returns a time string in given localisation and timezone info
  const formatter = new Intl.DateTimeFormat(undefined, {
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short',
    timeZone: timezone,
  })
  return formatter.format(date)
}

export function getTimeZoneOffset(timeZone: string, refDate: Date = new Date()): number {
  // Returns the timezone offset in milliseconds for a given timezone and reference date (For summer/winter date)
  const timezonedDate = new Date(refDate.toLocaleString('en-US', { timeZone }))
  const utcDate = new Date(refDate.toLocaleString('en-US', { timeZone: 'UTC' }))
  return utcDate.getTime() - timezonedDate.getTime()
}

export function toISOStringInTimezone(date: Date, timezone: string): string {
  // Returns the date in ISO date format in the specified timezone
  const timezoneOffset = getTimeZoneOffset(timezone, date)
  const adjustedDate = new Date(date.getTime() - timezoneOffset)
  return adjustedDate.toISOString().slice(0, 16)
}

export function dateInTimezone(date: string, timezone: string): Date {
  // From Timezoned ISO string returns the date adjusted to the offset
  const localDate = new Date(date)
  const timezoneOffset = getTimeZoneOffset(timezone, localDate)
  const localOffset = localDate.getTimezoneOffset() * 60 * 1000
  const adjustedDate = new Date(localDate.getTime() + timezoneOffset - localOffset)
  return adjustedDate
}
