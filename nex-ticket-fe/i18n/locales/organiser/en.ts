export default {
  organiser: {
    reporting: {
      organiser: {
        event_revenue_breakdown: 'Event Revenue Breakdown',
        events_breakdown: 'Events Breakdown',
        event: 'Event',
        total_revenue: 'Total Revenue',
        organiser_report: 'Organiser Report',
        show_all_data: 'Show All Data',
        start_date: 'Start Date',
        end_date: 'End Date',
        loading_data: 'Loading data...',
        sales_trend: 'Sales Trend',
        daily_sales: 'Daily Sales',
        event_revenue: 'Event Revenue',
        trend: 'Trend',
        no_data: 'No data available',
      },
      event: {
        event_report: 'Event Report',
        show_all_data: 'Show All Data',
        start_date: 'Start Date',
        end_date: 'End Date',
        loading_event_data: 'Loading event data...',
        sales_trend_by_ticket_type: 'Sales Trend by Ticket Type',
        ticket_types_distribution: 'Ticket Types Distribution',
        ticket_type_performance: 'Ticket Type Performance',
        ticket_type: 'Ticket Type',
        sold: 'Sold',
        revenue: 'Revenue',
        total_revenue: 'Total Revenue',
        tickets_sold: 'Tickets Sold',
        ticket_types: 'Ticket Types',
        no_data_loaded_info: 'Ups, it seems that no data for your stats was found. Relaod the page or contact the web administator.',
      },
    },
    data: {
      save_changes: 'Save Changes',
      saving_changes: 'Saving Changes...',
      edit_info: 'Edit organiser information',
      contact_mail: 'Contact Email',
      name: 'Organiser Name',
      contact_mobile: 'Contact Mobile',
      reg_number: 'Registration Number',
      vat_number: 'VAT Number',
      country: 'Country',
      default_currency: 'Default Currency',
      error_creating_event: 'Error creating event',
      profile_image: {
        profile_image: 'Upload Profile Image',
        profile_image_info: 'Recommended ratio: 16:9 (e.g. 1280×720)',
        crop: 'Crop your image',
        confirm_crop: 'Confirm Crop',
        cancel: 'Cancel',
      },
    },
    messages: {
      update_successful: 'Organiser successfully updated!',
      update_failed: 'Failed to update organiser',
    },
    show_details: 'Show Details',
    delete_event: {
      text: {
        confirm_del: 'Confirm Deletion',
        sec_confirm_del: 'Are you sure you want to delete this event? This action cannot be undone.',
      },
      buttons: {
        cancel: 'Cancel',
        delete: 'Delete',
        delete_event: 'Delete Event',
      },
    },
    create_event: {
      next: 'Next',
      prev: 'Previous',
    },
    edit_event: {
      data_invalid: 'The changes you made are not valid!',
      event_editing: 'Event editing',
      buttons: {
        edit_event: 'Edit Event',
        basic_setting: 'Basic Setting',
        location: 'Location',
        time: 'Time',
        additional_sett: 'Additional Settings',
        gallery: 'Gallery',
        tickets: 'Tickets',
        save_changes: 'Save Changes',
      },
      basic_settings_comp: {
        event_creation: 'Event creation',
        basic_settings: 'Basic settings',
        cover_image: {
          cover_image: 'Upload Cover Image',
          cover_image_info: 'Recommended ratio: 16:9 (e.g. 1280×720)',
          crop: 'Crop your image',
          confirm_crop: 'Confirm Crop',
          cancel: 'Cancel',
        },
        labels: {
          event_name: 'Event name',
          cover_image: 'Cover image',
          description: 'Event Description',
          start_time: 'Start time',
          end_time: 'End time',
        },
        place_holders: {
          event_name: 'Vienna new year party',
          description: 'The new year party in Vienna is the best party in the world',
        },
        yup_texts: {
          event_name_required: 'Event name is required',
          event_name_min: 'Event name must be at least 3 characters long',
          event_name_max: 'Event name must be at most 50 characters long',
          cover_image_required: 'Cover image is required',
          description_required: 'Description is required',
          description_min: 'Description must be at least 10 characters long',
          description_max: 'Description must be at most 2500 characters long',
          start_time_required: 'Start time is required',
          start_time_min: 'Start time must be in the future',
          end_time_required: 'End time is required',
          end_time_min: 'End time must be after start time',
        },
      },
      addit_sett_comp: {
        titel: 'Adittional Settings',
        links: 'Links',
        policies: 'Policies',
        add_social: 'Add Social Media Link',
        add_policy: 'Add Policy',
        tags: 'Tags',
        labels: {
          platform: 'Platform',
          link: 'Link',
          socials: 'Social media links',
          policy_type: 'Policy type',
          policy_details: 'Policy details',
          policies: 'Policies',
          tag_ids: 'Tag IDs',
        },
        place_holders: {
          platform: 'Select a platform',
          policy_type: 'Type ex. Age +18',
          policy_details: 'Details ex. No access for underaged people allowed',
        },
        yup_texts: {
          platform_required: 'Platform is required',
          invalid_platform: 'Invalid platform',
          invalid_url_format: 'Invalid URL format',
          link_required: 'Link is required',
          socials_required: 'Social media link is required',
          policy_type_required: 'Policy type is required',
          policy_type_min: 'Policy type must be at least 3 characters long',
          policy_type_max: 'Policy type must be at most 50 characters long',
          policy_details_required: 'Policy details are required',
          policy_details_min: 'Policy details must be at least 5 characters long',
          policy_details_max: 'Policy details must be at most 500 characters long',
          tag_id_required: 'Tag ID is required',
          tag_id_positive: 'Tag ID must be a positive number',
        },
      },
      gallery_comp: {
        labels: {
          images_gallery: 'Images gallery',
          loaded_image: 'Image',
          upload_failed: 'Upload failed',
          raw_file: 'Raw file',
          file: 'File',
          key: 'Key',
          event_data: 'Event data',
          photo_gallery: 'Photo gallery',
        },
        yup_texts: {
          invalid_url_format: 'Invalid URL format',
          valid_image_url: 'Photo gallery must contain valid image URLs',
        },
      },
      tickets_comp: {
        tickets_attrb: 'Ticket Attributes',
        discounts: 'Discounts',
        add_discount: 'Add Discount',
        description: 'Ticket\'s description',
        add_feature: 'Add Feature',
        add_ticket_type: 'Add Ticket Type',
        labels: {
          ticket_name: 'Ticket Type Name',
          max_amount: 'Max Amount Of Tickets',
          available_amount: 'Available Amount',
          price: 'Price',
          ticket_category: 'Ticket Category',
          percentage: 'Percentage',
          start_date: 'Start Date',
          end_date: 'End Date',
          feature_name: 'Feature Name',
          description: 'Description',
        },
        place_holders: {
          ticket_type_name: 'VIP, Regular, etc.',
          description: 'Energic concert with pop music and...',
        },
        yup_texts: {
          name_req: 'Name of Ticket is required',
          max_amount_req: 'Max amount is required',
          max_amount_min: 'Max amount must be at least 1',
          available_amnt_req: 'Available amount is required',
          available_amnt_min: 'Available amount must be at least 1',
          price_req: 'Price is required',
          price_min: 'Price must be at least 0',
          percentage_req: 'Percentage is required',
          percentage_min: 'Percentage must be at least 1%',
          percentage_max: 'Percentage must be at most 100%',
          start_date_req: 'Start date is required',
          end_date_req: 'End date is required',
          end_date_min: 'End date must be after start date',
          description_req: 'Description is required',
          description_min: 'Description must be at least 10 characters long',
          description_max: 'Description must be at most 2500 characters long',
          // tckt_features_req: 'Ticket features are required',
        },
      },
      location_comp: {
        titel: 'Location',
        labels: {
          venue_name: 'Venue name',
          latitude: 'Latitude',
          longitude: 'Longitude',
        },
        place_holders: {
          venue_name: 'Ministry Club',
        },
        yup_texts: {
          venue_name_required: 'Venue name is required',
          venue_name_min: 'Venue name must be at least 3 characters long',
          venue_name_max: 'Venue name must be at most 50 characters long',
          latitude_required: 'Latitude is required',
          latitude_range: 'Latitude must be between -90 and 90',
          longitude_required: 'Longitude is required',
          longitude_range: 'Longitude must be between -180 and 180',
        },
      },
    },
  },
}
