import type { I18nTranslation } from '@/utils/constants'
import { sub } from 'date-fns'
import Confirm_email from '~/pages/confirm_email.vue'
import Unlock_account from '~/pages/unlock_account.vue'

export default {
  auth: {
    fields: {
      // Step 1
      email: 'Email',
      first_name: 'First name',
      last_name: 'Last name',
      password: 'Password',
      password_confirmation: 'Password confirmation',

      // Step 2
      organiser_name: 'Organisation name',
      contact_email: 'Contact email',
      contact_mobile: 'Contact mobile',
      reg_number: 'Company registration number',
      vat_number: 'VAT number',
      country: 'Country',

      mobile: 'Mobile number',

      name: 'Name',
      subject: 'Subject',
      contact_message: 'Contact message',
      contact_message_placeholder: 'Enter your message',

      label_with_optional: ({ linked, named }: I18nTranslation) => `${linked(`auth.fields.${named('fieldName')}`)} (Optional)`,
    },
    buttons: {
      login: 'Login',
      submit: 'Submit',
      forgot_password: 'Forgot password?',
      register: 'Register',
      not_registered: 'Not registered?',
      has_account: 'Already have an account?',
      next: 'Next',
      prev: 'Previous',
      show_password: 'Show password',
    },
    organiser_login: {
      title: 'Organiser login',
    },
    login: {
      welcome: '🙌  Welcome back!',
      title: 'Login',
      subtitle: 'Ready to create unforgettable events?',
      dont_have_account: 'Don\'t have an account? ',
      sign_up: 'Sign up.',
      forgotten_password: 'Forgot Password?',
      change_password: 'Change Password.',
    },
    registration: {
      welcome: '🙌  Welcome!',
      title: 'Register',
      subtitle: 'To start publishing events',
      have_account: 'Already have an account? ',
      sign_in: 'Sign in.',
      step_1: 'Basic information',
      step_2: 'Organisation information',
    },
    organiser_registration: {
      title: 'Organiser registration',
      step_1: 'Basic information',
      step_2: 'Organisation information',
      password_mismatch: 'Passwords do not match',
    },
    payment: {
      continue: 'Continue to Payment',
      processing: 'Processing...',
      pay_now: 'Pay Now',
      canceled: 'Payment Canceled',
      success: 'Payment Successful!',
      success_confirm_1: 'Your tickets for',
      success_confirm_2: 'have been booked successfully.',
      canceled_confirm: 'Your payment was canceled. You can safely return to our website.',
      buttons: {
        home: 'Back to Home',
      },
    },
    forgotten_pswd: {
      change: 'Change password',
      your_email: 'Your email',
      send: 'Send',
      email: 'E-mail',
      sent_email: 'Email will be sent to your e-mail address, there you can change your password.',
      success: 'An email has been sent to your email address with instructions to reset your password.',
      error: 'Error sending email. Please try again.',
    },
    reset_password: {
      title: 'Change password',
      new_password: 'New password',
      confirm_password: 'Confirm password',
      submit: 'Reset password',
      success: 'Password changed successfully!',
      error: 'Error changing password. Please try again.',
    },
    confirm_email: {
      submit: 'Confirm your email',
      success: 'Your email has been successfully confirmed.',
      error: 'Error confirming email. Please try again.',
    },
    unlock_account: {
      submit: 'Unlock your account',
      success: 'Your account has been successfully unlocked.',
      error: 'Error unlocking account. Please try again.',
    },
  },
}
