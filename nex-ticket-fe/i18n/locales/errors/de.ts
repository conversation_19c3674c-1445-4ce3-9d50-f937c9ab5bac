export default {
  errors: {
    parse_error: 'Ein unerwarteter Fehler ist aufgetreten.',
    unexpected_error: 'Unerwart<PERSON>hler: ',
    reporting_error: '<PERSON><PERSON> beim <PERSON>: ',
    unknown_fail: 'Unbekannter Fehler aufgetreten',
    load_organiser_data_error: '<PERSON><PERSON> beim Laden der Veranstalterdaten: ',
    fetch_tags_error: '<PERSON>hler beim Abrufen der Tags: ',
    google_autocomplete_init_fail: 'Google Autocomplete wurde nicht initialisiert.',
    fetch_data_error: 'Fehler beim Abrufen der Daten: ',
    initialize_stripe_fail: 'Stripe konnte nicht initialisiert werden.',
    invalid_phone: 'Falsches Format, Ländervorwahl erforderlich, z.B. +49 für Deutschland',
    invalid_vat: 'Muss mit ein oder zwei Buchstaben beginnen, g<PERSON><PERSON><PERSON> von <PERSON>, oder nur aus Ziffern bestehen',
    order_errors: {
      load_basket_error: 'Fehler beim Laden des Warenkorbs: ',
      update_quantity_fail: 'Mengenaktualisierung fehlgeschlagen: ',
      create_order_fail: 'Bestellung konnte nicht erstellt werden.',
    },
    event_errors: {
      create_event_error: 'Fehler beim Erstellen der Veranstaltung: ',
      delete_event_fail: 'Veranstaltung konnte nicht gelöscht werden.',
      fetch_saved_event_error: 'Fehler beim Abrufen gespeicherter Veranstaltungen: ',
      fetch_grouped_event_data_error: 'Fehler beim Abrufen gruppierter Veranstaltungsdaten: ',
      fetch_events_error: 'Fehler beim Abrufen von Veranstaltungen: ',
      load_event_fail: 'Veranstaltung konnte nicht geladen werden.',
      save_changes_event_error: 'Fehler beim Speichern der geänderten Veranstaltung: ',
      delete_event_error: 'Fehler beim Löschen der Veranstaltung: ',
      save_event_error: 'Fehler beim Speichern der Veranstaltung: ',
      update_event_fail: 'Veranstaltung konnte nicht aktualisiert werden.',
      disable_last_ticket_type_error: 'Der letzte Ticket-Typ kann nicht deaktiviert werden.',
      city_missing_error: 'Für die Verwendung von Filtern muss die Stadt ausgewählt werden'
    },
    payment_errors: {
      load_payment_form_fail: 'Fehler beim Laden des Zahlungsformulars',
      payment_element_error: 'Fehler im Zahlungselement: ',
      create_checkout_error: 'Fehler beim Erstellen der Checkout-Sitzung: ',
      initialize_payment_fail: 'Zahlung konnte nicht initialisiert werden. Bitte aktualisieren Sie die Seite.',
    },
    file_errors: {
      invalid_file_input_event_fail: 'Ungültiges Dateieingabeereignis: ',
      read_image_file_error: 'Bilddatei konnte nicht gelesen werden: ',
      invalid_file_error: 'Ungültige Datei: ',
      upload_image_error: 'Bild konnte nicht hochgeladen werden: ',
      delete_image_error: 'Bild konnte nicht gelöscht werden: ',
      image_index_invalid_error: 'Ungültiger Bildindex: ',
      retry_fail: 'Keine Datei für einen erneuten Versuch verfügbar',
      upload_error: 'Upload fehlgeschlagen: ',
    },
  },
}