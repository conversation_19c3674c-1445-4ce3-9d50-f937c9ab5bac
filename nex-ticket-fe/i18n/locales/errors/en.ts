export default {
  errors: {
    parse_error: 'An unexpected error occurred.',
    unexpected_error: 'Unexpected error: ',
    reporting_error: 'Reporting error: ',
    unknown_fail: 'Unknown error occurred',
    load_organiser_data_error: 'Error loading organiser data: ',
    fetch_tags_error: 'Error fetching tags: ',
    google_autocomplete_init_fail: 'Google autocomplete was not initialized.',
    fetch_data_error: 'Error fetching data: ',
    initialize_stripe_fail: '<PERSON><PERSON> failed to initialize.',
    invalid_phone: 'Incorrect format mandatory country code eg. +48 for Poland',
    invalid_vat: 'Must start with one or two letters followed by digits, or only digits',
    order_errors: {
      load_basket_error: 'Basket load error: ',
      update_quantity_fail: 'Quantity update failed: ',
      create_order_fail: 'Failed to create order.',
    },
    event_errors: {
      create_event_error: 'Error creating event: ',
      delete_event_fail: 'Failed to delete event.',
      fetch_saved_event_error: 'Error fetching saved events: ',
      fetch_grouped_event_data_error: 'Error fetching grouped event data: ',
      fetch_events_error: 'Error fetching events: ',
      load_event_fail: 'Failed to load event.',
      save_changes_event_error: 'Error saving changed event: ',
      delete_event_error: 'Error deleting event: ',
      save_event_error: 'Error saving event: ',
      update_event_fail: 'Failed to update event.',
      disable_last_ticket_type_error: 'Cannot disable the last ticket type.',
      city_missing_error: 'City needs to be selected to use filters'
    },
    payment_errors: {
      load_payment_form_fail: 'Error loading payment form',
      payment_element_error: 'Payment element error: ',
      create_checkout_error: 'Error creating checkout session: ',
      initialize_payment_fail: 'Failed to initialize payment. Please refresh.',
    },
    file_errors: {
      invalid_file_input_event_fail: 'Invalid file input event: ',
      read_image_file_error: 'Failed to read image file: ',
      invalid_file_error: 'Invalid file: ',
      upload_image_error: 'Failed to upload image: ',
      delete_image_error: 'Failed to delete image: ',
      image_index_invalid_error: 'Invalid image index: ',
      retry_fail: 'No file available for retry',
      upload_error: 'Upload failed: ',
    },
  },
}
