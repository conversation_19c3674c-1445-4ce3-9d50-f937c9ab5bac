<script lang="ts" setup>
import { add<PERSON>ethod, default<PERSON><PERSON>ale, set<PERSON>ocale, object as yupObject, ref as yupRef, string as yupString } from 'yup'
import YupPassword from 'yup-password'

// @ts-expect-error - The types are correct in this point, but TS is not able to infer them
YupPassword({ setLocale, defaultLocale, addMethod, string: yupString })

const { t } = useI18n()
const { resetPassword } = useAuthStore()

const passwordResetSchema = computed(() => {
  return toTypedSchema(
    yupObject({
      password: yupString().required().min(8).max(30).minLowercase(1).minUppercase(1).minNumbers(1).minSymbols(1).label(t('auth.fields.password')),
      password_confirmation: yupString().required().oneOf([yupRef('password'), ''], t('auth.organiser_registration.password_mismatch')).label(t('auth.fields.password_confirmation')),
    }),
  )
})

const { validate: validatePasswordReset } = useForm({
  validationSchema: passwordResetSchema,
})

const password = useField<string>('password')
const password_confirmation = useField<string>('password_confirmation')

const token = useRoute().query.token?.toString()
const nuxtApp = useNuxtApp()

function displayError() {
  nuxtApp.$toast.error(t('auth.reset_password.error'))
}

async function submit() {
  if (token === undefined) {
    displayError()
    return
  }
  const { valid: is_valid } = await validatePasswordReset()
  if (!is_valid) {
    displayError()
    return
  }
  const is_reset = await resetPassword(token, password.value.value, password_confirmation.value.value)
  if (!is_reset) {
    displayError()
    return
  }
  nuxtApp.$toast.success(t('auth.reset_password.success'))
  navigateToWLocale('/')
}
</script>

<template>
  <div class="px-6 py-8 md:gap-12 md:justify-center">
    <form @submit.prevent="submit">
      <div class="flex flex-col gap-3 [&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white">
        <v-text-field
          v-model="password.value.value"
          variant="outlined" :label="t('auth.reset_password.new_password')" type="password" append-inner-icon="mdi-lock"
          :error-messages="password.errorMessage.value"
        />
      </div>
      <div class="flex flex-col gap-3 [&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white">
        <v-text-field
          v-model="password_confirmation.value.value"
          variant="outlined" :label="t('auth.reset_password.confirm_password')" type="password" append-inner-icon="mdi-lock"
          :error-messages="password_confirmation.errorMessage.value"
        />
      </div>
      <button type="submit" class="hidden transition-basic" />
    </form>
    <div
      class="bg-pie-700 rounded-lg p-1 hover:bg-pie-600 transition-colors duration-75 ease-out"
      @click="submit"
    >
      <div class="rounded-md border py-3 px-6 border-pie-300 text-lg-bold text-pie-50 flex flex-row items-center justify-center gap-1">
        {{ t('auth.reset_password.submit') }}
      </div>
    </div>
  </div>
</template>

<style>

</style>
