<script lang="ts" setup>
interface PromoCodeData {
  code: string | null
  description: string | null
  discount_type: string | null
  discount_value: number | null
  valid_from: string | null
  valid_until: string | null
  max_uses: number | null
  max_uses_per_user: number | null
  min_order_amount: number | null
  active: boolean
  applicable_event_ids: number[]
  applicable_ticket_type_ids: number[]
}

const promoCode = ref<PromoCodeData>({
  code: null,
  description: null,
  discount_type: null,
  discount_value: null,
  valid_from: null,
  valid_until: null,
  max_uses: null,
  max_uses_per_user: null,
  min_order_amount: null,
  active: true,
  applicable_event_ids: [],
  applicable_ticket_type_ids: [],
})

const feedback = useFeedback()
const { t } = useI18n()
const formErrors = ref<Record<string, string>>({})
const generalError = ref<string>('')
const successMessage = ref<string>('')
const isLoading = ref(false)
const organiserStore = useOrganiserStore()
const events = ref<{ id: number, name: string }[]>([])
const ticketTypes = ref<{ id: number, name: string }[]>([])
const loadingEvents = ref(false)
const loadingTickets = ref(false)
const error = ref<string>('')

const discountTypes = [
  { title: 'Percentage off total order', value: 'percentage_total' },
  { title: 'Fixed amount off total order', value: 'fixed_amount_total' },
]

async function createPromoCode() {
  try {
    isLoading.value = true
    formErrors.value = {}
    generalError.value = ''
    successMessage.value = ''

    const payload = { ...promoCode.value }

    const { data, error: apiError } = await useAPI('/api/organiser/promo_codes', {
      method: 'POST',
      body: payload,
    })

    if (apiError.value) {
      if (apiError.value.status === 422 && apiError.value.data?.errors) {
        if (typeof apiError.value.data.errors === 'object' && !Array.isArray(apiError.value.data.errors)) {
          formErrors.value = Object.entries(apiError.value.data.errors).reduce(
            (acc, [field, messages]) => {
              acc[field] = (messages as string[]).join(', ')
              return acc
            },
            {} as Record<string, string>,
          )
        }
        else {
          generalError.value = apiError.value.data?.error || apiError.value.data?.errors?.join(', ') || 'Validation failed.'
        }
      }
      else if (apiError.value.data?.error) {
        generalError.value = apiError.value.data.error
      }
      else {
        generalError.value = apiError.value.message || 'Failed to create promo code'
      }
      return
    }

    if (data.value) {
      feedback.info('Promo code successfully created!')
      await navigateToWLocale('/organiser/promo_codes')
    }
  }
  catch (err: unknown) {
    feedback.error('Unexpected error creating promo code:', { level: 'error', rollbar: true, extras: err })
    generalError.value = err instanceof Error ? err.message : 'An unknown error occurred'
  }
  finally {
    isLoading.value = false
  }
}

function resetForm() {
  promoCode.value = {
    code: null,
    description: null,
    discount_type: null,
    discount_value: null,
    valid_from: null,
    valid_until: null,
    max_uses: null,
    max_uses_per_user: null,
    min_order_amount: null,
    active: true,
    applicable_event_ids: [],
    applicable_ticket_type_ids: [],
  }
  formErrors.value = {}
  generalError.value = ''
  successMessage.value = ''
}

async function loadEvents() {
  try {
    loadingEvents.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/event_tickets_promo_code/events', {})

    if (fetchError.value) {
      console.error(t('errors.fetch_events_error'), fetchError.value)
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      events.value = data.value as { id: number, name: string }[]
    }
  }
  catch (err: any) {
    console.error(t('errors.unexpected_error'), err)
    error.value = err.message
  }
  finally {
    loadingEvents.value = false
  }
}

async function loadTicketTypes() {
  try {
    loadingTickets.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/event_tickets_promo_code/tickets', {})

    if (fetchError.value) {
      console.error(t('errors.fetch_ticket_types_error'), fetchError.value)
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      ticketTypes.value = data.value as { id: number, name: string }[]
    }
  }
  catch (err: any) {
    console.error(t('errors.unexpected_error'), err)
    error.value = err.message
  }
  finally {
    loadingTickets.value = false
  }
}

onMounted(() => {
  loadEvents()
  loadTicketTypes()
})
</script>

<template>
  <div class="md:w-3/4 lg:w-1/2 md:mx-auto md:mt-10 md:p-6 md:rounded-xl">
    <v-container>
      <div class="text-2xl-bold text-center mb-4 md:text-4xl-bold md:text-center md:mb-6">
        {{ $t('promo_codes.new.title', 'Create New Promo Code') }}
      </div>

      <v-alert v-if="generalError" type="error" density="compact" class="mb-2 md:mb-4">
        {{ generalError }}
      </v-alert>

      <v-alert v-if="successMessage" type="success" density="compact" class="mb-2 md:mb-4">
        {{ successMessage }}
      </v-alert>

      <form class="space-y-3" @submit.prevent="createPromoCode">
        <v-text-field
          v-model="promoCode.code"
          :label="$t('promo_codes.attributes.code', 'Code')"
          variant="outlined"
          density="compact"
          :error-messages="formErrors.code"
          required
        />

        <v-textarea
          v-model="promoCode.description"
          :label="$t('promo_codes.attributes.description', 'Description')"
          variant="outlined"
          density="compact"
          rows="2"
          :error-messages="formErrors.description"
        />

        <v-select
          v-model="promoCode.discount_type"
          :items="discountTypes"
          item-title="title"
          item-value="value"
          :label="$t('promo_codes.attributes.discount_type', 'Discount Type')"
          variant="outlined"
          density="compact"
          :error-messages="formErrors.discount_type"
          required
        />

        <v-text-field
          v-model.number="promoCode.discount_value"
          :label="$t('promo_codes.attributes.discount_value', 'Discount Value')"
          type="number"
          step="0.01"
          min="0"
          variant="outlined"
          density="compact"
          :error-messages="formErrors.discount_value"
          required
        />
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <v-text-field
            v-model="promoCode.valid_from"
            :label="$t('promo_codes.attributes.valid_from', 'Valid From (Optional)')"
            type="datetime-local"
            variant="outlined"
            density="compact"
            clearable
            :error-messages="formErrors.valid_from"
          />
          <v-text-field
            v-model="promoCode.valid_until"
            :label="$t('promo_codes.attributes.valid_until', 'Valid Until (Optional)')"
            type="datetime-local"
            variant="outlined"
            density="compact"
            clearable
            :error-messages="formErrors.valid_until"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <v-text-field
            v-model.number="promoCode.max_uses"
            :label="$t('promo_codes.attributes.max_uses', 'Max Uses (Overall, Optional)')"
            type="number"
            min="1"
            step="1"
            variant="outlined"
            density="compact"
            clearable
            :error-messages="formErrors.max_uses"
          />

          <v-text-field
            v-model.number="promoCode.max_uses_per_user"
            :label="$t('promo_codes.attributes.max_uses_per_user', 'Max Uses Per Customer (Optional)')"
            type="number"
            min="1"
            step="1"
            variant="outlined"
            density="compact"
            clearable
            :error-messages="formErrors.max_uses_per_user"
          />
        </div>

        <v-text-field
          v-model.number="promoCode.min_order_amount"
          :label="$t('promo_codes.attributes.min_order_amount', 'Minimum Order Amount (Optional)')"
          type="number"
          step="0.01"
          min="0"
          variant="outlined"
          density="compact"
          clearable
          :error-messages="formErrors.min_order_amount"
        />

        <div>
          <v-autocomplete
            v-model="promoCode.applicable_event_ids"
            class="text-base-normal"
            :items="events"
            :loading="loadingEvents"
            label="Applicable Event IDs"
            placeholder="Events Name"
            multiple
            chips
            closable-chips
            item-title="name"
            item-value="id"
            :error-messages="formErrors.applicable_event_ids"
          >
            <template #chip="{ props, item }">
              <v-chip class="text-base-normal" v-bind="props" :text="item.raw.name" />
            </template>
            <template #item="{ props, item }">
              <v-list-item class="text-sm-normal" v-bind="props" :title="item.raw.name" />
            </template>
          </v-autocomplete>
        </div>

        <div>
          <v-autocomplete
            v-model="promoCode.applicable_ticket_type_ids"
            class="text-base-normal"
            :items="ticketTypes"
            :loading="loadingTickets"
            label="Applicable Ticket Type IDs"
            placeholder="Tickets types"
            multiple
            chips
            closable-chips
            item-title="name"
            item-value="id"
            :error-messages="formErrors.applicable_ticket_type_ids"
          >
            <template #chip="{ props, item }">
              <v-chip class="text-base-normal" v-bind="props" :text="item.raw.name" />
            </template>
            <template #item="{ props, item }">
              <v-list-item class="text-sm-normal" v-bind="props" :title="item.raw.name" />
            </template>
          </v-autocomplete>
        </div>
        <v-switch
          v-model="promoCode.active"
          :label="$t('promo_codes.attributes.active', 'Active')"
          color="primary"
          inset
          :error-messages="formErrors.active"
        />

        <button
          type="submit"
          :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
          class="transition-basic w-full flex justify-center py-2 px-4 rounded-md shadow-sm text-base-medium text-white bg-primary hover:bg-primary-dark"
          :disabled="isLoading"
        >
          <span v-if="isLoading">Creating...</span>
          <span v-else>{{ $t('promo_codes.new.submit_button', 'Create Promo Code') }}</span>
        </button>
      </form>
    </v-container>
  </div>
</template>
