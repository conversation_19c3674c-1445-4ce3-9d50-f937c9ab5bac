<script lang="ts" setup>
import { useAPI } from '@/composables/useAPI'
import NexEvent from '@/models/NexEvent'

const { t } = useI18n()

const events = ref<NexEvent[]>([])
const { pageLoading } = storeToRefs(useGeneralStore())
const error = ref<string>('')
const feedback = useFeedback()

onMounted(async () => {
  await loadData()
})

async function loadData() {
  try {
    pageLoading.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/events')

    if (fetchError.value) {
      feedback.error(t('errors.event_errors.fetch_events_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      events.value = NexEvent.create_from_request(data.value) as NexEvent[]
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message
  }
  finally {
    pageLoading.value = false
  }
}
</script>

<template>
  <div class="rounded-2xl py-4 px-0">
    <!-- main content -->
    <div
      v-if="!pageLoading"
      class="md:grid md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4
        flex flex-col gap-8 justify-items-center items-center"
    >
      <div v-for="event in events" :key="event.id">
        <!-- PC version -->
        <EventBlock
          :id="event?.id"
          class="hidden md:block"
          :main-photo-url="event?.main_photo?.url"
          :name="event?.name"
          :venue-name="event?.venue_name"
          :venue-address="event?.venue_address"
          :start-time="event?.start_time"
          :end-time="event?.end_time"
          :time-zone="event?.timezone"
          mode="organiser"
          block-width-class="w-[302px]"
          photo-height-class="h-[154px]"
        />

        <!-- Mobile version -->
        <EventBlock
          :id="event?.id"
          class="md:hidden"
          :main-photo-url="event?.main_photo?.url"
          :name="event?.name"
          :venue-name="event?.venue_name"
          :venue-address="event?.venue_address"
          :start-time="event?.start_time"
          :end-time="event?.end_time"
          :time-zone="event?.timezone"
          mode="organiser"
          block-width-class="w-[328px]"
          photo-height-class="h-[164px]"
        />
      </div>
    </div>

    <!-- placeholders while loading data -->
    <div
      v-else
      class="md:grid md:grid-cols-2 md:md:grid-cols-3 md:lg:grid-cols-4 md:xl:grid-cols-4
        flex flex-col gap-8 items-center"
    >
      <div v-for="index in 12" :key="index">
        <!-- PC version -->
        <EventBlock
          :id="index"
          class="hidden md:block"
          main-photo-url=""
          name="dummy_name"
          venue-name="dummy_name"
          venue-address="dummy_address"
          :start-time="new Date()"
          :end-time="new Date()"
          mode="organiser"
          block-width-class="w-[302px]"
          photo-height-class="h-[154px]"
        />

        <!-- Mobile version -->
        <EventBlock
          :id="index"
          class="md:hidden"
          main-photo-url=""
          name="dummy_name"
          venue-name="dummy_name"
          venue-address="dummy_address"
          :start-time="new Date()"
          :end-time="new Date()"
          mode="organiser"
          block-width-class="w-[328px]"
          photo-height-class="h-[164px]"
        />
      </div>
    </div>
  </div>
</template>
