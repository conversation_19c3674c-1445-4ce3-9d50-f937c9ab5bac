<script lang="ts" setup>
import NexEvent from '@/models/NexEvent'
import { useRoute } from 'vue-router'

interface LocalImageType {
  isLoaded: boolean
  uploadFailed: boolean
  rawFile?: string
  url?: string
  file?: File
  key?: string
}

const feedback = useFeedback()

const { t } = useI18n()

const route = useRoute()
const { pageLoading } = storeToRefs(useGeneralStore())
const error = ref<string>('')
const event = ref<NexEvent | null>(null)
const openedPanel = ref<0 | 1 | 2 | 3 | 4 | 5>(0)
const serverResponse = ref<null | any>(null)
const eventId = ref<string>('')

const compValidateFunc = ref<() => Promise<boolean>>()
const showInvalidMessage = ref(false)

const basicSettData = ref<{ name: string, cover_image_key: string, cover_image_url: string, description: string }>()
const locationData = ref<{ latitude: number, longitude: number, venue_name: string, venue_address: string, timezone: string }>()
const timeData = ref<{ start_time: Date, end_time: Date }>()
const additionalSettData = ref<{
  socials: { platform: string, link: string }[]
  policies: { type: string, details: string }[]
  tag_ids: number[]
}>()
const galleryData = ref<LocalImageType[]>([])
const ticketsData = ref<{
  id: number
  name: string
  max_amount: number
  price: number
  discounts: { percentage: number, start_date: Date, end_date: Date }[]
  description: string
  disabled: boolean | undefined
}[]>([])

onMounted(async () => {
  eventId.value = route.params.id as string
  await loadEventData(eventId.value)
})

async function loadEventData(eventId: string) {
  try {
    pageLoading.value = true
    const { data: fetchedData, error: fetchError } = await useAPI(
      `/api/organiser/events/${eventId}`,
      {
        query: { includes: 'ticket_types,tags',
        },
      },
    )

    if (fetchError.value) {
      error.value = fetchError.value.message
      return
    }

    event.value = NexEvent.create_from_request(fetchedData.value) as NexEvent
    presetPanelsData()
  }
  catch (err: unknown) {
    error.value = err instanceof Error ? err.message : t('errors.event_errors.load_event_fail')
  }
  finally {
    pageLoading.value = false
  }
}

function showErrorMessage() {
  showInvalidMessage.value = true
  setTimeout(() => {
    showInvalidMessage.value = false
  }, 4000) // Display for 4 seconds
}

function presetPanelsData() {
  if (event.value) {
    basicSettData.value = {
      name: event.value.name,
      cover_image_key: event.value.main_photo?.key || '',
      cover_image_url: event.value.main_photo?.url || '',
      description: event.value.description,
    }

    locationData.value = {
      latitude: event.value.latitude,
      longitude: event.value.longitude,
      venue_name: event.value.venue_name,
      venue_address: event.value.venue_address,
      timezone: event.value.timezone,
    }

    timeData.value = {
      start_time: event.value.start_time,
      end_time: event.value.end_time,
    }

    additionalSettData.value = {
      socials: event.value.social_media_links?.map(social => ({ platform: capitalize(social.platform), link: social.link })) || [],
      policies: event.value.policies?.map(policy => ({ type: policy.type, details: policy.details })) || [],
      tag_ids: event.value.tags?.map(tag => Number(tag.id)) || [],
    }

    galleryData.value = event.value.photo_gallery?.map(photo => ({
      isLoaded: true,
      uploadFailed: false,
      rawFile: '',
      url: photo.url,
      file: undefined,
      key: photo.key,
    })) || []

    event.value.ticket_types?.forEach((type) => {
      if (typeof type !== 'number') {
        ticketsData.value.push({
          id: type.id,
          name: type.name,
          max_amount: type.max_amount,
          price: Number(type.price),
          discounts: type.discounts?.map(discount => ({
            percentage: discount.percentage,
            start_date: new Date(discount.start_date),
            end_date: new Date(discount.end_date),
          })) || [],
          description: type.description,
          disabled: type.disabled,
        })
      }
    })
  }
}

async function changePanel(targetPanel: 0 | 1 | 2 | 3 | 4 | 5) {
  if (await validateData()) {
    openedPanel.value = targetPanel
  }
  else {
    showErrorMessage()
  }
}

async function validateData() {
  if (compValidateFunc.value && await compValidateFunc.value()) {
    return true
  }
  else {
    return false
  }
}

async function onSaveChangesButtPress() {
  if (await validateData()) {
    saveChanges()
  }
  else {
    showErrorMessage()
  }
}

function isEventDataChanged() {
  if (event.value && basicSettData.value && locationData.value && timeData.value && additionalSettData.value && galleryData.value && ticketsData.value) {
    if (event.value.name !== basicSettData.value.name)
      return true
    if (event.value.description !== basicSettData.value.description)
      return true
    if (event.value.start_time !== timeData.value.start_time)
      return true
    if (event.value.end_time !== timeData.value.end_time)
      return true
    if (event.value.latitude !== locationData.value.latitude)
      return true
    if (event.value.longitude !== locationData.value.longitude)
      return true
    if (event.value.venue_name !== locationData.value.venue_name)
      return true
    if (event.value.main_photo.url !== basicSettData.value.cover_image_url)
      return true
    if (event.value.main_photo.key !== basicSettData.value.cover_image_key)
      return true
    if (event.value.social_media_links !== additionalSettData.value.socials.map(social => ({ platform: social.platform.toLowerCase(), link: social.link })))
      return true
    if (event.value.policies !== additionalSettData.value.policies)
      return true
    if (event.value.tags !== additionalSettData.value.tag_ids.map(tag_id => ({ id: tag_id })))
      return true
    if (event.value.photo_gallery !== galleryData.value.map(photo => ({ url: photo.url, key: photo.key })))
      return true

    event.value.ticket_types?.forEach((type, index) => {
      if (typeof type !== 'number') {
        if (type.name !== ticketsData.value[index].name)
          return true
        if (type.max_amount !== ticketsData.value[index].max_amount)
          return true
        if (type.price !== ticketsData.value[index].price)
          return true
        if (type.discounts !== ticketsData.value[index].discounts.map(discount => ({
          percentage: discount.percentage,
          start_date: discount.start_date,
          end_date: discount.end_date,
        }))) { return true }
        if (type.description !== ticketsData.value[index].description)
          return true
      }
    })
  }
  return false
}

async function saveChanges() {
  if (basicSettData.value && locationData.value && timeData.value && additionalSettData.value && galleryData.value && ticketsData.value && isEventDataChanged()) {
    const newEventData = {
      event: {
        name: basicSettData.value.name,
        description: basicSettData.value.description,
        start_time: timeData.value.start_time,
        end_time: timeData.value.end_time,
        venue_name: locationData.value.venue_name,
        main_photo: basicSettData.value.cover_image_key,
        longitude: locationData.value.longitude,
        latitude: locationData.value.latitude,
        social_media_links: additionalSettData.value.socials.map(social => ({
          platform: social.platform.toLowerCase(),
          link: social.link,
        })),
        policies: additionalSettData.value.policies.map(policy => ({
          type: policy.type,
          details: policy.details,
        })),
        tags: additionalSettData.value.tag_ids,
        photo_gallery: galleryData.value.map(photo => photo.key),
        ticket_types_attributes: ticketsData.value.map(ticket => ({
          id: ticket.id,
          name: ticket.name,
          max_amount: ticket.max_amount,
          price: ticket.price,
          discounts: ticket.discounts.map(discount => ({
            percentage: discount.percentage,
            start_date: discount.start_date,
            end_date: discount.end_date,
          })),
          disabled: ticket.disabled,
          description: ticket.description,
        })),
      },
    }
    try {
      const { error: fetchError } = await useAPI(`/api/organiser/events/${eventId.value}`, {
        method: 'PATCH',
        body: newEventData,
      })
      if (fetchError.value) {
        feedback.error(t('errors.event_errors.save_changes_event_error'), { level: 'error', rollbar: true, extras: fetchError.value })
        serverResponse.value = fetchError.value.message
      }
      else {
        navigateToWLocale(`/organiser/events/${eventId.value}`)
      }
    }
    catch (err: any) {
      feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
      serverResponse.value = err.message
    }
  }
}
</script>

<template>
  <div>
    <div>
      <h3
        class="md:text-5xl-bold md:mt-0
          text-2xl-bold m-auto text-center mt-2 text-slate-800"
      >
        {{ $t('organiser.edit_event.event_editing') }}
      </h3>
    </div>
    <HorizontalClicker
      v-model:selected-button="openedPanel"
      :buttons-text-keys="[
        { textKey: 'organiser.edit_event.buttons.basic_setting' },
        { textKey: 'organiser.edit_event.buttons.location' },
        { textKey: 'organiser.edit_event.buttons.time' },
        { textKey: 'organiser.edit_event.buttons.additional_sett' },
        { textKey: 'organiser.edit_event.buttons.gallery' },
        { textKey: 'organiser.edit_event.buttons.tickets' },
      ]"
      @clicked="changePanel"
    />

    <!-- Error State -->
    <div
      v-if="error"
      class="md:p-4 md:text-2xl-medium
        p-3 text-red-500 text-center text-xl-medium"
    >
      {{ error }}
    </div>

    <!-- placeholders while loading data -->
    <div v-else-if="pageLoading">
      <v-card flat>
        <v-container>
          <v-row>
            <v-container>
              <div class="space-y-4 mb-6">
                <!-- Photo preview -->
                <div>
                  <div class="relative rounded-lg overflow-hidden border border-slate-600">
                    <div
                      class="md:px-40 md:py-[10.35rem]
                      object-cover flex justify-between px-10 bg-slate-200 items-center py-[3.3rem]"
                    >
                      <div class="placeholder-circle md:size-80 size-28 animate-pulse" />
                      <div class="placeholder-circle md:size-60 size-20 animate-pulse" />
                      <div class="placeholder-circle md:size-40 size-10 animate-pulse" />
                    </div>
                  </div>
                </div>

                <!-- Rest of Step 1 Form -->
                <div class="placeholder animate-pulse w-full h-14" />
                <!-- PC version of Dates selectors -->
                <div class="hidden md:block pt-6">
                  <v-row>
                    <v-col cols="6">
                      <div class="placeholder animate-pulse w-full h-14" />
                    </v-col>
                    <v-col cols="6">
                      <div class="placeholder animate-pulse w-full h-14" />
                    </v-col>
                  </v-row>
                </div>

                <!-- Mobile version of Dates selectors -->
                <div class="md:hidden">
                  <v-row class="pt-6">
                    <v-col cols="12">
                      <div class="placeholder animate-pulse w-full h-14" />
                    </v-col>
                  </v-row>
                  <v-row class="pt-6">
                    <v-col cols="12">
                      <div class="placeholder animate-pulse w-full h-14" />
                    </v-col>
                  </v-row>
                </div>
                <v-row class="pt-6">
                  <v-col cols="12">
                    <div class="placeholder animate-pulse w-full h-[9.5rem]" />
                  </v-col>
                </v-row>
              </div>
            </v-container>
          </v-row>
        </v-container>
      </v-card>
    </div>

    <!-- Event Content -->
    <div v-else>
      <div>
        <EventEditBasicSettingsPanel
          v-if="openedPanel === 0"
          v-model:event-data="basicSettData"
          v-model:validate-func="compValidateFunc"
        />
        <EventEditLocationPanel
          v-if="openedPanel === 1"
          v-model:event-data="locationData"
          v-model:validate-func="compValidateFunc"
        />
        <EventEditTimePanel
          v-if="openedPanel === 2"
          v-model:event-data="timeData"
          v-model:validate-func="compValidateFunc"
          :timezone="locationData?.timezone"
        />
        <EventEditAdditionalSetPanel
          v-if="openedPanel === 3"
          v-model:event-data="additionalSettData"
          v-model:validate-func="compValidateFunc"
        />
        <EventEditGalleryPanel
          v-if="openedPanel === 4"
          v-model:event-data="galleryData"
          v-model:validate-func="compValidateFunc"
        />
        <EventEditTicketsPanel
          v-if="openedPanel === 5"
          v-model:event-data="ticketsData"
          v-model:validate-func="compValidateFunc"
          :timezone="locationData?.timezone"
        />
      </div>
      <div>
        <v-col cols="12" class="text-center">
          <v-btn
            class="text-base-normal"
            large
            color="#1e293b"
            @click="onSaveChangesButtPress()"
          >
            {{ $t('organiser.edit_event.buttons.save_changes') }}
          </v-btn>
          <div v-if="serverResponse">
            <p
              class="md:text-xl-bold md:pt-4
                text-lg-bold text-red-700 pt-3 text-center"
            >
              {{ serverResponse }}
            </p>
          </div>
        </v-col>
        <transition name="fade">
          <p
            v-if="showInvalidMessage"
            class="md:text-xl-bold md:pt-4
              text-lg-bold text-red-700 pt-3 text-center"
          >
            {{ $t('organiser.edit_event.data_invalid') }}
          </p>
        </transition>
      </div>
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>
