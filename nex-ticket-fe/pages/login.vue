<script lang="ts" setup>
const { t } = useI18n()

const authStore = useAuthStore()

const email = ref<string>('')
const password = ref<string>('')

async function login() {
  await authStore.login(email.value, password.value, '/organiser')
}

const showPassword = ref(false)

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}
</script>

<template>
  <div class="min-h-screen w-full">
    <!-- Main container with full width and responsive max-width -->
    <div class="w-full max-w-[125rem] mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
        <!-- Left side - Images (hidden on mobile) -->
        <div class="relative hidden lg:flex justify-center p-8 ">
          <div class="relative w-full max-w-[35rem] h-[40rem] mt-36 ml-12 scale-125">
            <!-- Main login image -->
            <div class="absolute rounded-2xl z-10">
              <img
                src="/assets/images/login.png"
                alt="Login main image"
                class="w-full h-full object-cover"
              >
            </div>
          </div>
        </div>

        <!-- Right side - Login form -->
        <div class="flex justify-center p-4 lg:p-8">
          <div class="w-full max-w-[36rem]">
            <!-- Welcome badge -->
            <div class="mb-6">
              <div class="inline-flex items-center gap-2 rounded-lg border py-3 px-4 bg-pie-100 border-pie-300 text-base-normal text-pie-950">
                {{ $t('auth.login.welcome') }}
              </div>
            </div>

            <!-- Title and subtitle -->
            <div class="mb-8">
              <h1 class="text-5xl md:text-6xl lg:text-7xl font-extrabold text-pie-700 font-sofia uppercase tracking-[-0.02em] mb-2">
                {{ $t('auth.login.title') }}
              </h1>
              <p class="text-base-normal md:text-lg-normal text-slate-500">
                {{ $t('auth.login.subtitle') }}
              </p>
            </div>

            <!-- Login form container -->
            <div class="rounded-2xl border p-6 md:p-8 bg-pie-25 border-slate-300 shadow-sm">
              <div class="space-y-6">
                <!-- Form fields -->
                <form class="space-y-4" @submit.prevent="login">
                  <div class="space-y-4">
                    <!-- Email field -->
                    <div>
                      <v-text-field
                        v-model="email"
                        variant="outlined"
                        :label="t('auth.fields.email')"
                        append-inner-icon="mdi-email"
                        class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                        density="comfortable"
                      />
                    </div>

                    <!-- Password field -->
                    <div>
                      <v-text-field
                        v-model="password"
                        variant="outlined"
                        :label="t('auth.fields.password')"
                        :type="showPassword ? 'text' : 'password'"
                        :append-inner-icon="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                        class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                        density="comfortable"
                        @click:append-inner="togglePasswordVisibility"
                      />
                    </div>
                  </div>
                  <button type="submit" class="hidden" />
                </form>

                <!-- Login button -->
                <NexButton
                  :text="t('auth.buttons.login')"
                  variant="primary"
                  :full-width="true"
                  :second-border="true"
                  text-style="text-lg-bold"
                  @click="login"
                />

                <!-- Sign up link -->
                <div class="text-center">
                  <p class="text-base-normal text-slate-600">
                    {{ $t('auth.login.dont_have_account') }}
                    <NuxtLinkLocale
                      to="/registration"
                      class="text-base-bold text-pie-700 hover:underline transition-basic"
                    >
                      {{ $t('auth.login.sign_up') }}
                    </NuxtLinkLocale>
                  </p>
                </div>

                <!-- Forgot password link -->
                <div class="text-center pt-2">
                  <p class="text-base-normal text-slate-600">
                    {{ $t('auth.login.forgotten_password') }}
                    <NuxtLinkLocale
                      to="/forgot_password"
                      class="text-base-bold text-pie-700 hover:underline transition-basic"
                    >
                      {{ $t('auth.login.change_password') }}
                    </NuxtLinkLocale>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Autofill styling */
:deep(input:-webkit-autofill),
:deep(input:-webkit-autofill:hover),
:deep(input:-webkit-autofill:focus),
:deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #000 !important;
  caret-color: #000;
  transition: background-color 9999s ease-in-out 0s;
}

/* Vuetify field styling */
:deep(.v-field__overlay) {
  background-color: white !important;
}

:deep(.v-field--outlined .v-field__outline) {
  --v-field-border-opacity: 0.38;
}

:deep(.v-field--outlined.v-field--focused .v-field__outline) {
  --v-field-border-opacity: 1;
  color: rgb(73 42 224); /* pie-700 */
}

/* Responsive image adjustments */
@media (max-width: 1024px) {
  .login-images {
    display: none;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(-2deg);
  }
  50% {
    transform: translateY(-10px) rotate(-2deg);
  }
}
</style>
