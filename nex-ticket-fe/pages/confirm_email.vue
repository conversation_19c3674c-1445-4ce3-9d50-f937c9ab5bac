<script lang="ts" setup>
const { confirmEmail } = useAuthStore()
const token = useRoute().query.token?.toString()
const nuxtApp = useNuxtApp()

const { t } = useI18n()

async function submit() {
  if (token === undefined)
    return
  const result = await confirmEmail(token)
  if (result) {
    nuxtApp.$toast.success(t('auth.confirm_email.success'))
    navigateToWLocale('/login/')
  }
  else {
    nuxtApp.$toast.error(t('auth.confirm_email.error'))
  }
}
</script>

<template>
  <div class="px-6 py-8 md:gap-12 md:justify-center">
    <div
      class="bg-pie-700 rounded-lg p-1 hover:bg-pie-600 transition-colors duration-75 ease-out"
      @click="submit"
    >
      <div class="rounded-md border py-3 px-6 border-pie-300 text-lg-bold text-pie-50 flex flex-row items-center justify-center gap-1">
        {{ $t('auth.confirm_email.submit') }}
      </div>
    </div>
  </div>
</template>
