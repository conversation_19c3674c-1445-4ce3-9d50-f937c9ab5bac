<script lang="ts" setup>
import { ErrorMessage, Field, Form } from 'vee-validate'
import * as yup from 'yup'

const { t } = useI18n()

const email: Ref<string> = ref('')

const emailRules = yup.string().required().email()
const nuxtApp = useNuxtApp()

const { requestPasswordEmail } = useAuthStore()
async function handleSubmit() {
  const result = await requestPasswordEmail(email.value)
  if (result) {
    nuxtApp.$toast.success(t('auth.forgotten_pswd.success'))
    navigateToWLocale('/')
  }
  else {
    nuxtApp.$toast.error(t('auth.forgotten_pswd.error'))
  }
}
</script>

<template>
  <div class="px-6 py-8 md:p-0 md:gap-12 md:justify-center">
    <Form class="max-w-sm mx-auto" @submit="handleSubmit">
      <h1
        class="mb-4 text-center text-4xl-extrabold leading-none tracking-tight text-slate-800 text-5xl lg:text-6xl"
      >
        {{ $t('auth.forgotten_pswd.change') }}
      </h1>
      <div class="md:mb-5 md:relative">
        <label for="email" class="block mb-2 text-sm-medium text-slate-800">{{ $t('auth.forgotten_pswd.your_email') }}</label>
        <Field
          id="email" v-model="email" type="email" name="email" :rules="emailRules"
          class="shadow-sm bg-gray-50 border border-gray-300 text-slate-800 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
          placeholder="<EMAIL>"
        />
        <ErrorMessage name="email" />
      </div>
      <div>
        <button
          type="submit"
          class="transition-basic mb-5 text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 text-sm-medium rounded-lg w-full w-auto px-5 py-2.5 text-center"
        >
          {{ $t('auth.forgotten_pswd.send') }}
          {{ $t('auth.forgotten_pswd.email') }}
        </button>
      </div>
      <div>
        <p class="text-lg-normal text-black text-s">
          {{ $t('auth.forgotten_pswd.sent_email') }}
        </p>
      </div>
    </Form>
  </div>
</template>

<style></style>
