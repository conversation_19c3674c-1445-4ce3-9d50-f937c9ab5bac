<script lang="ts" setup>
const { unlockAccount } = useAuthStore()
const token = useRoute().query.token?.toString()
const nuxtApp = useNuxtApp()
const { t } = useI18n()

async function submit() {
  if (token === undefined)
    return
  const result = await unlockAccount(token)
  if (result) {
    nuxtApp.$toast.success(t('auth.unlock_account.success'))
    navigateToWLocale('/login/')
  }
  else {
    nuxtApp.$toast.error(t('auth.unlock_account.error'))
  }
}
</script>

<template>
  <div class="px-6 py-8 md:gap-12 md:justify-center">
    <div
      class="bg-pie-700 rounded-lg p-1 hover:bg-pie-600 transition-colors duration-75 ease-out"
      @click="submit"
    >
      <div class="rounded-md border py-3 px-6 border-pie-300 text-lg-bold text-pie-50 flex flex-row items-center justify-center gap-1">
        {{ $t('auth.unlock_account.submit') }}
      </div>
    </div>
  </div>
</template>
