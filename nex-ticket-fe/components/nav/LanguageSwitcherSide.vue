<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { ArrowLeft01Icon, ArrowRight01Icon } from 'hugeicons-vue'

const { locale, locales, t } = useI18n({ useScope: 'global' })

const availableLocales = computed(() => locales.value.filter(l => l.code !== locale.value))

const showLanguageOptions = ref<boolean>(false)
const switcherContainerRef = ref<HTMLElement | null>(null)

function toggleLanguageOptions() {
  showLanguageOptions.value = !showLanguageOptions.value
  if (showLanguageOptions.value === false) {
    const active = document.activeElement as HTMLElement | null
    active?.blur()
  }
}

onClickOutside(switcherContainerRef, () => {
  showLanguageOptions.value = false
})

function handleLocaleSelection() {
  showLanguageOptions.value = false
}
</script>

<template>
  <div ref="switcherContainerRef" class="relative w-full">
    <button
      type="button"
      class="flex items-center justify-between w-full px-4 py-3 rounded-md hover:bg-pie-800 text-base-normal text-white focus:outline-none focus:bg-pie-800 focus:ring-opacity-50"
      aria-haspopup="true"
      :aria-expanded="showLanguageOptions"
      @click="toggleLanguageOptions"
    >
      <span>{{ t(`nav.lang.${locale}`) }} ({{ locale.toUpperCase() }})</span>
      <ArrowRight01Icon v-if="!showLanguageOptions" class="w-4 h-4 ml-1 flex-shrink-0 text-white" />
      <ArrowLeft01Icon v-else class="w-4 h-4 ml-1 flex-shrink-0 text-white" />
    </button>

    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="showLanguageOptions"
        class="absolute top-0 left-full ml-2 min-w-max z-20 rounded-md bg-slate-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        role="menu"
        aria-orientation="vertical"
        @click.stop
      >
        <div role="none">
          <SwitchLocalePathLink
            v-for="loc in availableLocales"
            :key="loc.code"
            v-slot="{ href, navigate }"
            :locale="loc.code" custom
          >
            <a
              :href="href"
              role="menuitem" class="block w-full text-left px-4 py-3 text-base-normal text-white rounded-md hover:bg-pie-800"
              @click="navigate(); handleLocaleSelection()"
            >
              {{ t(`nav.lang.${loc.code}`) }}
            </a>
          </SwitchLocalePathLink>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
/* Minimal styling, relying on Tailwind */
</style>
