<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { ArrowDown01Icon, ArrowUp01Icon } from 'hugeicons-vue'

const { locale, locales, t } = useI18n({ useScope: 'global' })
const availableLocales = computed(() => locales.value.filter(l => l.code !== locale.value))

const dropdownOpen = ref<boolean>(false)
const switcherContainerRef = ref<HTMLElement | null>(null)

onClickOutside(switcherContainerRef, () => {
  dropdownOpen.value = false
})

function handleLocaleSelection() {
  dropdownOpen.value = false
}

function toggleDropdown() {
  dropdownOpen.value = !dropdownOpen.value
}
</script>

<template>
  <div ref="switcherContainerRef" class="md:relative">
    <button class="flex flex-row justify-between items-center md:justify-center md:gap-1 md:rounded-lg md:py-3 md:ps-6 md:pe-2 md:cursor-pointer" @click="toggleDropdown">
      <h3 class="md:text-xl-bold md:text-slate-800 text-sm-normal text-white">
        {{ locale.toUpperCase() }}
      </h3>
      <ArrowUp01Icon v-if="dropdownOpen" class="md:text-slate-800 text-white" />
      <ArrowDown01Icon v-else class="md:text-slate-800 text-white" />
    </button>
    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="dropdownOpen"
        class="z-30 mt-2 md:absolute md:mt-2 md:text-lg-medium md:min-w-max md:rounded-md md:shadow-lg md:bg-white md:ring-1 md:ring-black md:ring-opacity-5"
        role="menu"
        aria-orientation="vertical"
        @click.stop
      >
        <div role="none" class="md:py-1 flex flex-col gap-2">
          <SwitchLocalePathLink
            v-for="loc in availableLocales"
            :key="loc.code"
            v-slot="{ href, navigate }"
            :locale="loc.code" custom
            class="pl-5 md:block md:w-full md:text-left md:px-4 md:py-2 md:text-slate-800"
          >
            <a
              :href="href"
              role="menuitem" class="block w-full text-left px-4 py-3 text-sm-normal text-white rounded-md hover:bg-pie-800"
              @click.prevent="navigate(), handleLocaleSelection()"
            >
              {{ t(`nav.lang.${loc.code}`) }}
            </a>
          </SwitchLocalePathLink>
        </div>
      </div>
    </transition>
  </div>
</template>
