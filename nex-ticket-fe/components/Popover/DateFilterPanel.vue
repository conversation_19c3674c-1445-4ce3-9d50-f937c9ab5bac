<script lang="ts" setup>
import VueDatePicker from '@vuepic/vue-datepicker'
import { addMonths, isEqual, subMonths } from 'date-fns'
import { ArrowDown01Icon, ArrowLeft01Icon, ArrowRight01Icon, ArrowUp01Icon, Cancel01Icon } from 'hugeicons-vue'
import '@vuepic/vue-datepicker/dist/main.css'

const props = defineProps({
  filterNameKey: { type: String, required: true },
  minDate: { type: Date, default: null },
  placeholder: { type: String, default: 'Select date range' },
  timezone: { type: String, default: 'UTC' },
})

const emit = defineEmits(['apply'])

const { t } = useI18n()

const dateRange = defineModel<[Date | null, Date | null] | null>({ default: null })

const popoverOpened = ref(false)
const internalDateRange = ref<Date[] | null>(null)

const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
]

function goToPrevMonth(month: number, year: number, updateMonthYear?: (month: number, year: number, fromNav: boolean) => void) {
  const newDate = subMonths(new Date(year, month), 1)
  updateMonthYear?.(newDate.getMonth(), newDate.getFullYear(), true)
}

function goToNextMonth(month: number, year: number, updateMonthYear?: (month: number, year: number, fromNav: boolean) => void) {
  const newDate = addMonths(new Date(year, month), 1)
  updateMonthYear?.(newDate.getMonth(), newDate.getFullYear(), true)
}

const isFiltered = computed(() => {
  return Array.isArray(dateRange.value)
    && dateRange.value.length === 2
    && (dateRange.value[0] !== null || dateRange.value[1] !== null)
})

function togglePopover() {
  popoverOpened.value = !popoverOpened.value
  if (popoverOpened.value) {
    internalDateRange.value = dateRange.value ? dateRange.value.filter((date): date is Date => date !== null) : null
  }
}

function applyFilter() {
  const apply = () => {
    popoverOpened.value = false
    emit('apply')
  }

  const adjustDate = (date: Date) => {
    date.setHours(0, 0, 0, 0)
    const timezoneOffset = getTimeZoneOffset(props.timezone, date)
    const localOffset = date.getTimezoneOffset() * 60 * 1000
    return new Date(date.getTime() - localOffset + timezoneOffset)
  }

  if (JSON.stringify(internalDateRange.value) === JSON.stringify(dateRange.value)) {
    apply()
    return
  }

  if (!internalDateRange.value || internalDateRange.value.length !== 2) {
    dateRange.value = null
    apply()
    return
  }

  const adjustedDates = internalDateRange.value.map(adjustDate)
  dateRange.value = [adjustedDates[0], adjustedDates[1]]
  apply()
}

function resetFilter(event?: MouseEvent) {
  if (event) {
    event.stopPropagation()
  }
  dateRange.value = null
  internalDateRange.value = null
  popoverOpened.value = false
  emit('apply')
}

function cancel() {
  popoverOpened.value = false
  internalDateRange.value = dateRange.value ? dateRange.value.filter((date): date is Date => date !== null) : null
}

const containerRef = ref<HTMLElement | null>(null)

function handleClickOutside(event: MouseEvent) {
  if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
    popoverOpened.value = false
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})

const displayText = computed(() => {
  if (!isFiltered.value)
    return t(props.filterNameKey)

  if (!dateRange.value || (!dateRange.value[0] && !dateRange.value[1]))
    return t(props.filterNameKey)

  const [startDate, endDate] = dateRange.value

  if (startDate && endDate) {
    return `${formatDate(startDate)} - ${formatDate(endDate)}`
  }
  else if (startDate) {
    return `${formatDate(startDate)} →`
  }
  else if (endDate) {
    return `→ ${formatDate(endDate)}`
  }

  return t(props.filterNameKey)
})

function formatDate(date: Date | null) {
  if (!date)
    return ''
  const day = date.getDate()
  const month = date.toLocaleString(undefined, { month: 'short' })
  return `${day} ${month}`
}
</script>

<template>
  <div
    ref="containerRef"
    class="relative inline-block border rounded-lg"
    :class="isFiltered || popoverOpened ? 'bg-pie-100 border-pie-700 text-pie-700' : 'text-slate-600 border-slate-500 bg-slate-50 hover:bg-slate-100 hover:border-slate-600 hover:text-slate-700'"
  >
    <button
      type="button"
      class="flex items-center gap-1 px-4 py-3 md:px-6 md:py-3 w-full h-full text-sm-bold md:text-lg-medium rounded-lg whitespace-nowrap cursor-pointer"
      :class="isFiltered || popoverOpened ? '!text-pie-700' : '!text-slate-600'"
      @click="togglePopover"
    >
      {{ displayText }}
      <ArrowUp01Icon v-if="popoverOpened" :size="20" class="shrink-0" />
      <Cancel01Icon
        v-else-if="isFiltered"
        :size="20"
        class="shrink-0 hover:text-red-500"
        @click.stop="resetFilter"
      />
      <ArrowDown01Icon v-else :size="20" class="shrink-0" />
    </button>

    <transition>
      <div
        v-if="popoverOpened"
        class="absolute z-30 mt-2 w-auto origin-top-left left-0  bg-white border border-slate-200 rounded-lg shadow-xl p-4 "
        @click.stop
      >
        <VueDatePicker
          v-model="internalDateRange"
          range
          :min-date="props.minDate"
          inline
          :enable-time-picker="false"
          :placeholder="props.placeholder"
          auto-apply
          :close-on-auto-apply="false"
          class="my-custom-datepicker-theme"
        >
          <template #month-year="{ month, year, updateMonthYear }">
            <div
              v-if="month !== undefined && year !== undefined"
              class="flex items-center justify-between w-full"
            >
              <!-- Left Arrow -->
              <div class="p-2">
                <component
                  :is="ArrowLeft01Icon"
                  class="text-pie-950 cursor-pointer w-6 h-6"
                  @click="goToPrevMonth(month, year, updateMonthYear)"
                />
              </div>

              <!-- Combined Month and Year -->
              <span class="px-2 py-[10px] text-pie-950 text-xl-medium">
                {{ monthNames[month] }} {{ year }}
              </span>

              <!-- Right Arrow -->
              <div class="p-2">
                <component
                  :is="ArrowRight01Icon"
                  class="text-pie-950 cursor-pointer w-6 h-6"
                  @click="goToNextMonth(month, year, updateMonthYear)"
                />
              </div>
            </div>
          </template>
        </VueDatePicker>
        <div v-if="internalDateRange" class="px-3 py-6 flex w-full h-full">
          <button
            type="button"
            class="py-3 text-xl-bold w-full h-full !text-slate-100 bg-pie-600 border border-transparent rounded-md hover:bg-pie-700 transition-basic"
            :disabled="!internalDateRange || internalDateRange.length !== 2" @click="applyFilter"
          >
            {{ !isEqual(internalDateRange[0], internalDateRange[1]) ? `${formatDate(internalDateRange[0])} → ${formatDate(internalDateRange[1])}` : `${formatDate(internalDateRange[1])}` }}
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
.my-custom-datepicker-theme :deep() {
  --dp-border-radius: 0.5rem;
  --dp-font-family: Onest, sans-serif;
  --dp-font-size: 0.875rem;

  --dp-hover-color: #f1f5f9;
  --dp-hover-text-color: #1e293b;
  --dp-primary-color: #492AE0 !important;
  --dp-primary-disabled-color: #9085F7 !important;
  --dp-primary-text-color: #ffffff;
  --dp-secondary-color: #E9E8FD;
  --dp-disabled-color: #d1d5db;
  --dp-highlight-color: rgba(73, 42, 224, 0.1);
  --dp-range-between-dates-background-color: #E9E8FD;
  --dp-range-between-dates-text-color: var(--dp-text-color, #334155);
  --dp-range-between-border-color: var(--dp-primary-color, #492AE0);

  --dp-month-year-row-background: #F3F2FE;
  --dp-month-year-row-color: #334155;
  --dp-calendar-header-color: #64748b;
  --dp-calendar-header-separator: var(--dp-border-color);
  --dp-arrow-left-color: var(--dp-primary-color);
  --dp-arrow-right-color: var(--dp-primary-color);

  --dp-action-row-background: var(--dp-background-color);
  --dp-action-button-height: 35px;
  --dp-select-color: var(--dp-primary-color);
  --dp-cancel-color: #9ca3af;

  --dp-cell-size: 48px;
}

/* Active days */
.my-custom-datepicker-theme :deep(.dp--future),
.my-custom-datepicker-theme :deep(.dp__today) {
  border: 1px solid #CBD5E1;
  background-color: #F8FAFC;
  border-radius: 8px;
  color: #1C0F69;

  /* text-xl-medium */
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-family: 'Onest', sans-serif;
  font-weight: 500;
}

/* Inactive days */
.my-custom-datepicker-theme :deep(.dp--past),
.my-custom-datepicker-theme :deep(.dp__cell_offset) {
  border: 0px !important;
  background-color: #ffffff !important;
  color: #9ca3af !important;

  /* text-xl-medium */
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-family: 'Onest', sans-serif;
  font-weight: 500;
}

/* Gap between calendar days */
.my-custom-datepicker-theme :deep(.dp__calendar_row) {
  gap: 4px;
}

/* Dot under today */
.my-custom-datepicker-theme :deep(.dp__today) {
  position: relative;
}

.my-custom-datepicker-theme :deep(.dp__today::after) {
  content: '';
  position: absolute;
  bottom: 6px;
  left: 50%;
  transform: translateX(-50%);
  width: 5px;
  height: 5px;
  background-color: #492AE0;
  border-radius: 50%;
}

.my-custom-datepicker-theme :deep(.dp__today.dp__range_start::after) {
  background-color: white;
}

/* No border around the calendar*/
.my-custom-datepicker-theme :deep(.dp__menu) {
  border: none !important;
  box-shadow: none !important;
}

/* No line under days */
.my-custom-datepicker-theme :deep(.dp__calendar_header_separator) {
  visibility: hidden;
}

/* Selected days */
.my-custom-datepicker-theme :deep(.dp__range_between),
.my-custom-datepicker-theme :deep(.dp__range_end),
.my-custom-datepicker-theme :deep(.dp__range_start) {
  background-color: #492AE0;
  color: #ffffff;
}

/* Hover */
.my-custom-datepicker-theme :deep(.dp__date_hover) {
  border: 1px solid #492AE0;
  background-color: #E9E8FD;
  color: #1C0F69;
}

/* No hover on inactive */
.my-custom-datepicker-theme :deep(.dp__cell_offset.dp__date_hover) {
  border: 0px !important;
  background-color: #ffffff !important;
}

/* Text color */
.my-custom-datepicker-theme :deep(.dp__icon),
.my-custom-datepicker-theme :deep(.dp__calendar_header),
.my-custom-datepicker-theme :deep(.dp__month_year_select) {
  color: #1C0F69;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
