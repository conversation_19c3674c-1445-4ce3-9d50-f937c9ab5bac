<script lang="ts" setup>
interface Button {
  textKey: string
}

const props = defineProps<{
  buttonsTextKeys: But<PERSON>[]
}>()

const emit = defineEmits(['clicked'])

const selectedButton = defineModel<number>('selectedButton')
</script>

<template>
  <div
    class="md:py-2 md:px-0 md:overflow-x-auto md:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-6
      flex flex-row overflow-x-scroll items-center text-center"
  >
    <div v-for="buttonsTextKey, index in props.buttonsTextKeys" :key="index">
      <div>
        <button
          class="md:mx-0 md:hover:text-slate-800 md:hover:text-lg-medium
            p-2 mx-3 w-max transition-75 rounded-lg text-base-medium"
          :class="selectedButton === index ? 'text-slate-800 md:text-lg-medium' : 'text-slate-500 md:text-base-medium'"
          @click="emit('clicked', index)"
        >
          {{ $t(buttonsTextKey.textKey) }}
        </button>
      </div>
    </div>
  </div>
</template>

<style>

</style>
