<script lang="ts" setup>
import NexCity from '@/models/NexCity'
import { number as yupNumber, object as yupObject, string as yupString } from 'yup'
import { useEventFormStore } from '~/stores/eventDataAfterReload'

const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.location_comp.yup_texts.${key}`)
const label_t = (key: string) => t(`organiser.edit_event.location_comp.labels.${key}`)
const eventForm = useEventFormStore()

const nuxtApp = useNuxtApp()

const schema_step_2 = yupObject({
  venue_name: yupString()
    .required(yup_t('venue_name_required'))
    .min(3, yup_t('venue_name_min'))
    .max(50, yup_t('venue_name_max'))
    .label(label_t('venue_name')),
  latitude: yupNumber()
    .required(yup_t('latitude_required'))
    .min(-90, yup_t('latitude_range'))
    .max(90, yup_t('latitude_range'))
    .label(label_t('latitude')),
  longitude: yupNumber()
    .required(yup_t('longitude_required'))
    .min(-180, yup_t('longitude_range'))
    .max(180, yup_t('longitude_range'))
    .label(label_t('longitude')),
})
const { validate: validateStep2 } = useForm({
  validationSchema: schema_step_2,
})

const eventData = defineModel<{ latitude: number, longitude: number, venue_name: string, venue_address: string, timezone: string }>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

const latitude = useField<number>('latitude')
const longitude = useField<number>('longitude')
const venue_name = useField<string>('venue_name')
const city_error = ref('')

const { value: venueNameValue, errorMessage: venueNameError } = useField<string>(
  'venue_name',
  undefined,
  { initialValue: eventForm.eventVenueName },
)
const { value: latitudeValue, errorMessage: latitudeError } = useField<number>(
  'latitude',
  undefined,
  { initialValue: eventForm.eventLat },
)
const { value: longitudeValue, errorMessage: longitudeError } = useField<number>(
  'longitude',
  undefined,
  { initialValue: eventForm.eventLong },
)

watch(venueNameValue, (newVal) => {
  eventForm.setField('eventVenueName', newVal)
  if (eventData.value) { 
    eventData.value.venue_name = newVal
  }
})

watch(latitudeValue, (newVal) => {
  eventForm.setField('eventLat', newVal) 
  if (eventData.value) { 
    eventData.value.latitude = newVal
  }
})

watch(longitudeValue, (newVal) => {
  eventForm.setField('eventLong', newVal) 
  if (eventData.value) {
    eventData.value.longitude = newVal
  }
})

onMounted(async () => {
  validateFunc.value = validateFields

  venueNameValue.value = eventForm.eventVenueName
  latitudeValue.value = eventForm.eventLat
  longitudeValue.value = eventForm.eventLong

  if (eventData.value) {
    if (eventData.value.venue_name) {
      eventForm.setField('eventVenueName', eventData.value.venue_name)
      venueNameValue.value = eventData.value.venue_name
    }
    if (eventData.value.latitude) {
      eventForm.setField('eventLat', eventData.value.latitude)
      latitudeValue.value = eventData.value.latitude
    }
    if (eventData.value.longitude) {
      eventForm.setField('eventLong', eventData.value.longitude)
      longitudeValue.value = eventData.value.longitude
    }
  }

  await validateStep2()
})

async function updateTimezone() {
  if (!eventData.value) {
    return false
  }
  const { data, error } = await useAPI('/api/cities', {
    method: 'GET',
    query: {
      lat: latitude.value,
      lon: longitude.value,
    },
  })
  if (error.value) {
    nuxtApp.$toast.error('City not supported')
    return false
  }
  else {
    const city = NexCity.create_from_request(data.value)
    if (!city || Array.isArray(city)) {
      return false
    }
    eventData.value.timezone = city.timezone
    return true
  }
}

async function validateFields(): Promise<boolean> {
  const update_tz_promise = updateTimezone()
  const validate_promise = validateStep2()
  const { valid } = await validate_promise
  const update_tz = await update_tz_promise

  city_error.value = ''
  const { error } = await useAPI('/api/cities/supported', {
    method: 'GET',
    query: {
      lat: latitude.value,
      lon: longitude.value,
    },
  })
  if (error.value) {
    city_error.value = error.value.message
    return false
  }

  return valid && update_tz
}
</script>

<template>
  <v-card class="!shadow-none">
    <v-row class="pt-5 px-5">
      <v-col cols="12">
        <VTextField
          v-model="venue_name.value.value"
          class="font-onest"
          :label="$t('organiser.edit_event.location_comp.labels.venue_name')"
          :placeholder="$t('organiser.edit_event.location_comp.place_holders.venue_name')"
          :error-messages="venue_name.errorMessage.value"
          required
          variant="outlined"
          @update:model-value="eventData && (eventData.venue_name = venue_name.value.value)"
        />
      </v-col>
    </v-row>
    <div class="px-5 pb-5">
      <GoogleMapsOrganiser
        v-model:lat="latitude.value.value"
        v-model:lng="longitude.value.value"
        :city-error="city_error"
        height="h-svh"
        @update:lat="val => eventForm.setField('eventLat', val || 37.39094933041195)"
        @update:lng="val => eventForm.setField('eventLong', val || -122.02503913145092)"
      />
    </div>
  </v-card>
</template>

<style>

</style>
