<script lang="ts" setup>
import { date as yupDate, object as yupObject } from 'yup'

const props = defineProps({
  timezone: {
    type: String,
    default: 'UTC',
  },
})
const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.time_comp.yup_texts.${key}`)
const label_t = (key: string) => t(`organiser.edit_event.time_comp.labels.${key}`)

const schema_step_2 = yupObject({
  start_time: yupDate()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .required(yup_t('start_time_required'))
    .min(new Date(), yup_t('start_time_min'))
    .label(label_t('start_time')),
  end_time: yupDate()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .required(yup_t('end_time_required'))
    .test(
      'is-greater',
      yup_t('end_time_min'),
      function (value) {
        const { start_time } = this.parent
        return value > start_time
      },
    )
    .label(label_t('end_time')),
})
const { validate: validateStep2 } = useForm({
  validationSchema: schema_step_2,
})

const eventData = defineModel<{ start_time: Date, end_time: Date }>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

const start_time = useField<string>('start_time')
const end_time = useField<string>('end_time')

const updatedStartTime = ref<boolean>(false)
const updatedEndTime = ref<boolean>(false)

onMounted(async () => {
  validateFunc.value = validateFields
  setExistingeventData()
})

watch(() => props.timezone, () => {
  if (eventData.value) {
    if (updatedStartTime.value) {
      start_time.value.value = toISOStringInTimezone(eventData.value.start_time, props.timezone)
    }
    if (updatedEndTime.value) {
      end_time.value.value = toISOStringInTimezone(eventData.value.end_time, props.timezone)
    }
  }
})

function setExistingeventData() {
  if (eventData.value) {
    start_time.value.value = toISOStringInTimezone(eventData.value.start_time, props.timezone)
    end_time.value.value = toISOStringInTimezone(eventData.value.end_time, props.timezone)
  }
  else {
    eventData.value = {
      start_time: new Date(),
      end_time: new Date(),
    }
  }
}

async function validateFields(): Promise<boolean> {
  const { valid } = await validateStep2()
  return valid
}
</script>

<template>
  <v-card flat>
    <v-container>
      <!-- PC version of Dates selectors -->
      <div class="hidden md:block">
        <v-row>
          <v-col cols="6">
            <VTextField
              id="start_time"
              v-model="start_time.value.value"
              :label="$t('organiser.edit_event.basic_settings_comp.labels.start_time')"
              :error-messages="start_time.errorMessage.value"
              required
              variant="outlined"
              type="datetime-local"
              @update:model-value="eventData && (eventData.start_time = dateInTimezone(start_time.value.value, props.timezone)) && (updatedStartTime = true)"
            />
          </v-col>
          <v-col cols="6">
            <VTextField
              id="end_time"
              v-model="end_time.value.value"
              :label="$t('organiser.edit_event.basic_settings_comp.labels.end_time')"
              :error-messages="end_time.errorMessage.value"
              required
              variant="outlined"
              type="datetime-local"
              @update:model-value="eventData && (eventData.end_time = dateInTimezone(end_time.value.value, props.timezone)) && (updatedEndTime = true)"
            />
          </v-col>
        </v-row>
      </div>

      <!-- Mobile version of Dates selectors -->
      <div class="md:hidden">
        <v-row>
          <v-col cols="12">
            <VTextField
              id="start_time"
              v-model="start_time.value.value"
              :label="$t('organiser.edit_event.basic_settings_comp.labels.start_time')"
              :error-messages="start_time.errorMessage.value"
              required
              variant="outlined"
              type="datetime-local"
              @update:model-value="eventData && (eventData.start_time = dateInTimezone(start_time.value.value, props.timezone)) && (updatedStartTime = true)"
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <VTextField
              id="end_time"
              v-model="end_time.value.value"
              :label="$t('organiser.edit_event.basic_settings_comp.labels.end_time')"
              :error-messages="end_time.errorMessage.value"
              required
              variant="outlined"
              type="datetime-local"
              @update:model-value="eventData && (eventData.end_time = dateInTimezone(end_time.value.value, props.timezone)) && (updatedEndTime = true)"
            />
          </v-col>
        </v-row>
      </div>
    </v-container>
  </v-card>
</template>

<style>

</style>
