<script lang="ts" setup>
import type { RegistrationData } from '@/models/NexUser'
import { useAuthStore } from '@/stores/auth'
import { toTypedSchema } from '@vee-validate/yup'
import { ArrowRight01Icon } from 'hugeicons-vue'
import { addMethod, defaultLocale, setLocale, object as yupObject, ref as yupRef, string as yupString } from 'yup'
import YupPassword from 'yup-password'

// @ts-expect-error - The types are correct in this point, but TS is not able to infer them
YupPassword({ setLocale, defaultLocale, addMethod, string: yupString })

const { t } = useI18n()
const authStore = useAuthStore()

const currentStep = ref<0 | 1>(0)

// Step 1

const schema = computed(() => {
  return toTypedSchema(
    yupObject({
      first_name: yupString().required().min(5).max(30).label(t('auth.fields.first_name')),
      last_name: yupString().required().min(5).max(30).label(t('auth.fields.last_name')),
      email: yupString().email().required().label(t('auth.fields.email')),
      password: yupString().required().min(8).max(30).minLowercase(1).minUppercase(1).minNumbers(1).minSymbols(1).label(t('auth.fields.password')),
      password_confirmation: yupString().required().oneOf([yupRef('password'), ''], t('auth.organiser_registration.password_mismatch')).label(t('auth.fields.password_confirmation')),
    }),
  )
})

const { validate: validateStep1 } = useForm({
  validationSchema: schema,
})

const first_name = useField<string>('first_name')
const last_name = useField<string>('last_name')
const email = useField<string>('email')
const password = useField<string>('password')
const password_confirmation = useField<string>('password_confirmation')

// Step 2

const schema_step_2 = computed(() => {
  return toTypedSchema(
    yupObject({
      organiser_name: yupString().required().min(5).max(30).label(t('auth.fields.organiser_name')),
      contact_email: yupString().email().required().label(t('auth.fields.contact_email')),
      contact_mobile: yupString().required().test('is-valid-phone', t('errors.invalid_phone'), value => /^\+\d+$/.test(value || '')).label(t('auth.fields.contact_mobile')),
      reg_number: yupString().required().min(5).max(30).label(t('auth.fields.reg_number')),
      vat_number: yupString().matches(/^(?:[A-Z]{2})?\d+$/i, t('errors.invalid_vat')).label(t('auth.fields.vat_number')),
      country: yupString().required().label(t('auth.fields.country')),
    }),
  )
})

const { validate: validateStep2 } = useForm({
  validationSchema: schema_step_2,
})

const organiser_name = useField<string>('organiser_name')
const contact_email = useField<string>('contact_email')
const contact_mobile = useField<string>('contact_mobile')
const reg_number = useField<string>('reg_number')
const vat_number = useField<string>('vat_number')
const country = useField<string>('country')

function getRegistrationData(): RegistrationData {
  return {
    user: {
      email: email.value.value,
      first_name: first_name.value.value,
      last_name: last_name.value.value,
      password: password.value.value,
      type: 'OrganiserAccount',
      organiser_attributes: {
        name: organiser_name.value.value,
        contact_email: contact_email.value.value,
        contact_mobile: contact_mobile.value.value,
        reg_number: reg_number.value.value,
        vat_number: vat_number.value.value,
        state_id: country.value.value,
      },
    },
  }
}

async function onSubmit() {
  if ((await validateStep2()).valid) {
    await authStore.signup(getRegistrationData(), '/organiser')
  }
}

async function handleClick() {
  if (currentStep.value === 0) {
    if ((await validateStep1()).valid) {
      currentStep.value = 1
    }
  }
  else {
    onSubmit()
  }
}

async function handleStepClick(index: number): Promise<void> {
  if (index === 0) {
    currentStep.value = 0
  }
  else {
    if (currentStep.value === 0) {
      if ((await validateStep1()).valid) {
        currentStep.value = 1
      }
    }
  }
}

const showPassword = ref(false)
const showConfirmPassword = ref(false)

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
  showConfirmPassword.value = !showConfirmPassword.value
}
</script>

<template>
  <div class="min-h-screen w-full">
    <!-- Main container with full width and responsive max-width -->
    <div class="w-full max-w-[125rem] mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
        <!-- Left side - Images (hidden on mobile) -->
        <div class="relative hidden lg:flex justify-center p-8">
          <div class="relative w-full max-w-[35rem] h-[40rem] mt-36 ml-12 scale-125">
            <!-- Main registration image -->
            <div class="absolute rounded-2xl z-10">
              <img
                src="/assets/images/login.png"
                alt="Registration main image"
                class="w-full h-full object-cover"
              >
            </div>
          </div>
        </div>

        <!-- Right side - Registration form -->
        <div class="flex justify-center p-4 lg:p-8">
          <div class="w-full max-w-[64rem]">
            <!-- Welcome badge -->
            <div class="mb-6">
              <div class="inline-flex items-center gap-2 rounded-lg border py-3 px-4 bg-pie-100 border-pie-300 text-base-normal text-pie-950">
                {{ $t('auth.registration.welcome') }}
              </div>
            </div>

            <!-- Title and subtitle -->
            <div class="mb-8">
              <h1 class="text-5xl md:text-6xl lg:text-7xl font-extrabold text-pie-700 font-sofia uppercase tracking-[-0.02em] mb-2">
                {{ $t('auth.registration.title') }}
              </h1>
              <p class="text-base-normal md:text-lg-normal text-slate-500">
                {{ $t('auth.registration.subtitle') }}
              </p>
            </div>

            <!-- Registration form container -->
            <div class="rounded-2xl border p-6 md:p-8 bg-pie-25 border-slate-300 shadow-sm">
              <div class="space-y-6">
                <!-- Stepper -->
                <TicketSelectorCheckoutStepper
                  :steps="[
                    { label: $t('auth.registration.step_1') },
                    { label: $t('auth.registration.step_2') },
                  ]"
                  :current-step="currentStep"
                  @step-click="handleStepClick"
                />

                <!-- Form fields -->
                <form class="space-y-4" @submit.prevent="onSubmit">
                  <div v-if="currentStep === 0" class="space-y-4">
                    <!-- Step 1: Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <v-text-field
                        id="first_name"
                        v-model="first_name.value.value"
                        variant="outlined"
                        :label="t('auth.fields.first_name')"
                        append-inner-icon="mdi-account-arrow-left"
                        class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                        density="comfortable"
                        :error-messages="first_name.errorMessage.value"
                      />

                      <v-text-field
                        id="last_name"
                        v-model="last_name.value.value"
                        variant="outlined"
                        :label="t('auth.fields.last_name')"
                        append-inner-icon="mdi-account-arrow-right"
                        class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                        density="comfortable"
                        :error-messages="last_name.errorMessage.value"
                      />
                    </div>

                    <v-text-field
                      id="email"
                      v-model="email.value.value"
                      variant="outlined"
                      :label="t('auth.fields.email')"
                      append-inner-icon="mdi-email"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="email.errorMessage.value"
                    />

                    <v-text-field
                      id="password"
                      v-model="password.value.value"
                      variant="outlined"
                      :type="showPassword ? 'text' : 'password'"
                      :label="t('auth.fields.password')"
                      :append-inner-icon="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="password.errorMessage.value"
                      @click:append-inner="togglePasswordVisibility"
                    />

                    <v-text-field
                      id="password_confirmation"
                      v-model="password_confirmation.value.value"
                      variant="outlined"
                      :type="showConfirmPassword ? 'text' : 'password'"
                      :label="t('auth.fields.password_confirmation')"
                      :append-inner-icon="showConfirmPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="password_confirmation.errorMessage.value"
                      @click:append-inner="togglePasswordVisibility"
                    />
                  </div>
                  <div v-else class="space-y-4">
                    <!-- Step 2: Organisation Information -->
                    <v-text-field
                      id="organiser_name"
                      v-model="organiser_name.value.value"
                      variant="outlined"
                      :label="t('auth.fields.organiser_name')"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="organiser_name.errorMessage.value"
                    />

                    <v-text-field
                      id="contact_email"
                      v-model="contact_email.value.value"
                      variant="outlined"
                      :label="t('auth.fields.contact_email')"
                      append-inner-icon="mdi-email"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="contact_email.errorMessage.value"
                    />

                    <v-text-field
                      id="contact_mobile"
                      v-model="contact_mobile.value.value"
                      variant="outlined"
                      :label="t('auth.fields.contact_mobile')"
                      append-inner-icon="mdi-phone"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="contact_mobile.errorMessage.value"
                    />

                    <v-text-field
                      id="reg_number"
                      v-model="reg_number.value.value"
                      variant="outlined"
                      :label="t('auth.fields.reg_number')"
                      append-inner-icon="mdi-card-account-details"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="reg_number.errorMessage.value"
                    />

                    <v-text-field
                      id="vat_number"
                      v-model="vat_number.value.value"
                      variant="outlined"
                      :label="t('auth.fields.vat_number')"
                      append-inner-icon="mdi-receipt"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                      density="comfortable"
                      :error-messages="vat_number.errorMessage.value"
                      optional
                    />

                    <Autocomplete
                      id="country"
                      v-model="country.value.value"
                      label-key="auth.fields.country"
                      search-endpoint="/api/states"
                      variant="outlined"
                      :error-messages="country.errorMessage.value"
                      field-width="auto"
                      class="[&_.v-field]:rounded-lg [&_.v-field]:shadow-sm [&_.v-field]:bg-white [&_.v-field]:border-slate-300"
                    />
                  </div>
                  <button type="submit" class="hidden" />
                </form>

                <!-- Registration button -->
                <NexButton
                  v-if="currentStep === 0"
                  :text="t('auth.buttons.next')"
                  variant="primary"
                  :full-width="true"
                  :second-border="true"
                  text-style="text-lg-bold"
                  @click="handleClick"
                >
                  <template #append>
                    <ArrowRight01Icon class="w-5 h-5" />
                  </template>
                </NexButton>

                <NexButton
                  v-else
                  :text="t('auth.buttons.register')"
                  variant="primary"
                  :full-width="true"
                  :second-border="true"
                  text-style="text-lg-bold"
                  @click="handleClick"
                />

                <!-- Sign in link -->
                <div class="text-center">
                  <p class="text-base-normal text-slate-600">
                    {{ $t('auth.registration.have_account') }}
                    <NuxtLinkLocale
                      to="/login"
                      class="text-base-bold text-pie-700 hover:underline transition-basic"
                    >
                      {{ $t('auth.registration.sign_in') }}
                    </NuxtLinkLocale>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Autofill styling */
:deep(input:-webkit-autofill),
:deep(input:-webkit-autofill:hover),
:deep(input:-webkit-autofill:focus),
:deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #000 !important;
  caret-color: #000;
  transition: background-color 9999s ease-in-out 0s;
}

/* Vuetify field styling */
:deep(.v-field__overlay) {
  background-color: white !important;
}

:deep(.v-field--outlined .v-field__outline) {
  --v-field-border-opacity: 0.38;
}

:deep(.v-field--outlined.v-field--focused .v-field__outline) {
  --v-field-border-opacity: 1;
  color: rgb(73 42 224); /* pie-700 */
}

/* Responsive image adjustments */
@media (max-width: 1024px) {
  .registration-images {
    display: none;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(-2deg);
  }
  50% {
    transform: translateY(-10px) rotate(-2deg);
  }
}
</style>
