<script lang="ts" setup>
import NexTag from '@/models/NexTag'

const { t } = useI18n({ useScope: 'global' })
const feedback = useFeedback()

const tag_ids = defineModel<Array<number>>()

const search = ref('');

const tags = ref<NexTag[]>([])
const loading = ref(false)
const error = ref<string>('')

async function loadTags() {
  try {
    loading.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/tags', { })

    if (fetchError.value) {
      feedback.error(t('errors.fetch_tags_error:'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      tags.value = NexTag.create_instances(data.value as Array<object>)
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTags()
})
</script>

<template>
  <div>
    <v-autocomplete
      v-model="tag_ids"
      v-model:search="search"
      class="text-base-normal"
      :items="tags"
      :loading="loading"
      label="Select tags"
      multiple
      chips
      closable-chips
      item-title="name"
      item-value="id"
      @input="loadTags"
      @update:model-value="search = ''"
    >
      <template #chip="{ props, item }">
        <v-chip
          class="text-base-normal"
          v-bind="props"
          :text="item.raw.name"
        />
      </template>
      <template #item="{ props, item }">
        <v-list-item
          class="text-sm-normal"
          v-bind="props"
          :title="item.raw.name"
        />
      </template>
    </v-autocomplete>
  </div>
</template>

<style scoped>
/* Add your styles here */
</style>
