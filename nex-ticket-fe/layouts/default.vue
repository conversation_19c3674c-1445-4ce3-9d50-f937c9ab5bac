<script setup lang="ts">
import { Menu01Icon, Search01Icon, UserSharingIcon } from 'hugeicons-vue'

const route = useRoute()
const path = ref<string>(route.path)
const header = ref<HTMLElement | null>(null)
const isMobile = ref<boolean>(false)
const mobileHeaderStyle = ref<string>(getCustomStyle(path.value, 'mobile.header'))
const mobileHeaderBTNStyle = ref<string>(getCustomStyle(path.value, 'mobile.headerbtn'))

const isMenuOpen = ref<boolean>(false)
const isSearchOpen = ref<boolean>(false)

function checkMobileVersion(): void {
  const mobileDiv = header.value?.querySelector('.mobile-version')

  if (mobileDiv == null)
    return

  const displayStyle = window.getComputedStyle(mobileDiv).display
  isMobile.value = (displayStyle !== 'none')
}

watch(
  () => route.path,
  (newPath, _) => {
    path.value = newPath
    mobileHeaderStyle.value = getCustomStyle(path.value, 'mobile.header')
    mobileHeaderBTNStyle.value = getCustomStyle(path.value, 'mobile.headerbtn')
    // Close menus when route changes
    closeMenus()
  },
)

function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  const header = document.querySelector('header')

  if (header && !header.contains(target)) {
    closeMenus()
  }
}

onMounted(() => {
  window.addEventListener('resize', checkMobileVersion)
  document.addEventListener('click', handleClickOutside)
  checkMobileVersion()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkMobileVersion)
  document.removeEventListener('click', handleClickOutside)
})

async function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value
  // Close search if menu is opened
  if (isMenuOpen.value && isSearchOpen.value) {
    isSearchOpen.value = false
  }
}

function toggleSearch() {
  isSearchOpen.value = !isSearchOpen.value
  // Close menu if search is opened
  if (isSearchOpen.value && isMenuOpen.value) {
    isMenuOpen.value = false
  }
}

function closeMenus() {
  isMenuOpen.value = false
  isSearchOpen.value = false
}
</script>

<template>
  <div
    class="flex flex-col min-h-screen
                md:flex md:flex-col lg:min-h-screen bg-pie-25"
  >
    <div class="lg:min-h-screen flex flex-col">
      <!-- Header -->
      <header
        ref="header"
        class="bg-slate-50 border-b border-slate-300 shadow-sm w-full h-[4.5rem] flex items-center px-4
             md:bg-slate-50 md:border-slate-300 md:shadow-sm md:w-auto md:h-auto md:flex-row md:px-0"
        :style="isMobile ? mobileHeaderStyle : ''"
      >
        <!-- Desktop -->
        <div class="hidden justify-between items-center w-full h-full lg:flex lg:max-w-7xl lg:py-6 lg:m-auto lg:gap-6 md:px-2">
          <NuxtLinkLocale to="/" class="md:w-auto md:h-auto">
            <TicketPieLogo class="md:scale-100" />
          </NuxtLinkLocale>

          <nav class="md:w-full md:flex md:flex-row md:items-center md:justify-center md:gap-6 ">
            <NavSearchBar />
            <NexButton to="/search_events" text-key="nav.events" variant="secondary" class="md:inline-flex transition-basic" />
            <NexButton to="/registration" text-key="nav.for_organisers" variant="secondary" class="md:inline-flex transition-basic" />
            <NexButton to="/login" text-key="nav.login" :append-icon="UserSharingIcon" class="md:inline-flex transition-basic" />
            <NavLanguageSwitcher />
          </nav>
        </div>

        <!-- Mobile -->
        <div class="mobile-version lg:hidden flex justify-between items-center w-screen h-full py-3">
          <button class="transition-basic w-14 h-full place-items-center z-10" @click="toggleMenu">
            <Menu01Icon
              class="text-pie-950 stroke-2 transition-transform duration-200"
              :class="{ 'rotate-90': isMenuOpen }"
              :style="mobileHeaderBTNStyle"
            />
          </button>

          <NuxtLinkLocale to="/" class="w-auto h-auto z-10">
            <TicketPieLogo class="scale-75 md:scale-100" :is-mobile="true" />
          </NuxtLinkLocale>

          <button class="transition-basic w-14 h-full  place-items-center z-10" @click="toggleSearch">
            <Search01Icon
              class="text-pie-950 stroke-2 transition-transform duration-200"
              :class="{ 'scale-110': isSearchOpen }"
              :style="mobileHeaderBTNStyle"
            />
          </button>
        </div>
      </header>

      <!-- Mobile Menu -->
      <transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 transform -translate-y-2"
        enter-to-class="opacity-100 transform translate-y-0"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 transform translate-y-0"
        leave-to-class="opacity-0 transform -translate-y-2"
      >
        <div v-if="isMenuOpen" class="lg:hidden bg-white border-b border-slate-200 shadow-lg z-40">
          <nav class="flex flex-col gap-1 px-4 py-6">
            <div class="flex flex-col gap-3">
              <NexButton
                to="/search_events"
                text-key="nav.events"
                variant="secondary"
                class="w-full justify-start text-left transition-basic hover:bg-pie-50"
                @click="toggleMenu()"
              />
              <NexButton
                to="/registration"
                text-key="nav.for_organisers"
                variant="secondary"
                class="w-full justify-start text-left transition-basic hover:bg-pie-50"
                @click="toggleMenu()"
              />
              <NexButton
                to="/login"
                text-key="nav.login"
                :append-icon="UserSharingIcon"
                variant="secondary"
                class="w-full justify-start text-left transition-basic hover:bg-pie-50"
                @click="toggleMenu()"
              />
            </div>
            <div class="border-t border-slate-200 mt-4 pt-4">
              <NavLanguageSwitcher />
            </div>
          </nav>
        </div>
      </transition>

      <!-- Mobile SearchBar -->
      <transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 transform -translate-y-2"
        enter-to-class="opacity-100 transform translate-y-0"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 transform translate-y-0"
        leave-to-class="opacity-0 transform -translate-y-2"
      >
        <div v-if="isSearchOpen" class="lg:hidden bg-white border-b border-slate-200 shadow-lg z-40">
          <div class="px-4 py-6">
            <NavSearchBar />
          </div>
        </div>
      </transition>

      <!-- Main Content -->
      <main class="lg:flex-grow lg:container lg:mx-auto justify-items-center md:pt-8 lg:max-w-[105rem] md:max-w-[80rem]">
        <slot />
      </main>
    </div>
    <footer>
      <FooterNexPublicFooter />
    </footer>
  </div>
</template>
