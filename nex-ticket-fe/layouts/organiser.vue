<script lang="ts" setup>
import { NuxtLinkLocale } from '#components'
import { onClickOutside } from '@vueuse/core'
import { AnalysisTextLinkIcon, Edit02Icon, Logout03Icon, MoneyReceiveSquareIcon, Playlist01Icon, PromotionIcon, Settings01Icon } from 'hugeicons-vue'

const { t } = useI18n({ useScope: 'global' })
const authStore = useAuthStore()
const route = useRoute()

const activeNavText = ref(t('nav.organiser.title'))
const showSettingsMenu = ref(false)
const showMobileSettingsMenu = ref(false)

const navItems = ref([
  {
    path: '/organiser/events/create',
    icon: MoneyReceiveSquareIcon,
    text: t('nav.organiser.create_event'),
    group: 'events',
  },
  {
    path: '/organiser/events',
    icon: Playlist01Icon,
    text: t('nav.organiser.index_events'),
    group: 'events',
  },
  {
    path: '/organiser/reporting',
    icon: AnalysisTextLinkIcon,
    text: t('nav.organiser.reporting'),
    group: 'events',
  },
  {
    path: '/organiser/promo_codes',
    icon: PromotionIcon,
    text: t('nav.organiser.promo'),
    group: 'events',
  },
  {
    path: '/organiser/edit',
    icon: Edit02Icon,
    text: t('nav.organiser.edit_organiser'),
    group: 'organiser',
  },
])

const settingsRef = ref<HTMLElement | null>(null)
const mobileSettingsRef = ref<HTMLElement | null>(null)

onClickOutside(settingsRef, () => showSettingsMenu.value = false)
onClickOutside(mobileSettingsRef, () => showMobileSettingsMenu.value = false)

const groupedNavItems = computed(() => {
  const groups: Record<string, typeof navItems.value> = {}
  navItems.value.forEach((item) => {
    if (!groups[item.group]) {
      groups[item.group] = []
    }
    groups[item.group].push(item)
  })
  return groups
})

watchEffect(() => {
  const currentItem = navItems.value.find(item => item.path === route.path)
  activeNavText.value = currentItem ? currentItem.text : t('nav.organiser.title')
})

async function handleLogout(): Promise<void> {
  showSettingsMenu.value = false
  showMobileSettingsMenu.value = false
  await authStore.logout()
}
</script>

<template>
  <div class="flex flex-col lg:flex-row min-h-screen bg-pie-25">
    <div class="lg:hidden h-10 fixed top-0 left-0 bg-slate-600 p-1 text-white pl-4 text-left z-30 w-screen text-lg-bold">
      {{ activeNavText }}
    </div>

    <aside class=" hidden lg:sticky lg:top-0 lg:h-screen lg:w-56 lg:bg-slate-800 lg:shadow-lg lg:flex lg:flex-col z-20">
      <div class="lg:p-4 lg:text-2xl-bold lg:text-white">
        {{ t('nav.organiser.title') }}
      </div>
      <hr class="lg:border-slate-200 lg:mb-2">

      <nav class="lg:flex-1 lg:overflow-y-auto">
        <div v-for="(items, groupName) in groupedNavItems" :key="groupName" class="lg:mb-6">
          <h3 class="lg:px-5 lg:py-2 lg:text-base-semibold lg:text-slate-400 lg:uppercase">
            {{ String(groupName) }}
          </h3>
          <ul class="lg:space-y-1 lg:p-2">
            <li v-for="item in items" :key="item.path">
              <NuxtLinkLocale
                :to="item.path"
                class="lg:flex lg:items-center lg:p-3 lg:rounded-lg lg:hover:bg-pie-800 lg:text-white lg:transition-colors"
                active-class="bg-pie-700"
              >
                <component :is="item.icon" class="lg:w-5 lg:h-5 lg:mr-3 lg:text-white flex-shrink-0" />
                <span class="truncate">{{ item.text }}</span>
              </NuxtLinkLocale>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Footer Section -->
      <div ref="settingsRef" class="lg:mt-auto lg:p-2 lg:border-t lg:border-slate-700">
        <div class="relative">
          <button
            type="button"
            class="lg:w-full lg:flex lg:items-center lg:p-3 lg:rounded-lg lg:hover:bg-pie-800 lg:!text-white lg:transition-colors gap-2"
            aria-haspopup="true"
            :aria-expanded="showSettingsMenu"
            @click="showSettingsMenu = !showSettingsMenu"
          >
            <Settings01Icon class="w-5 h-5 flex-shrink-0" />
            <span class="lg:text-base-medium">{{ t('nav.organiser.settings') }}</span>
          </button>

          <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-x-95"
            enter-to-class="transform opacity-100 scale-x-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-x-100"
            leave-to-class="transform opacity-0 scale-x-95"
          >
            <div
              v-if="showSettingsMenu"
              class="absolute left-full ml-3 bottom-0 mb-2 w-56 origin-left transform rounded-md bg-slate-700 shadow-lg"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="settings-menu-button"
              @click.stop
            >
              <div role="none">
                <div class=" focus:bg-slate-600 text-white rounded-md" role="none">
                  <NavLanguageSwitcherSide />
                </div>
                <div class="border-t border-slate-white mx-1" role="none" />
                <button
                  type="button"
                  class="flex items-center w-full px-4 py-3 hover:bg-pie-800 gap-2 rounded-md"
                  role="menuitem"
                  @click="handleLogout"
                >
                  <Logout03Icon class="w-4 h-4 text-white stroke-2" />
                  <span class="text-white text-base-normal">{{ t('nav.organiser.logout') }}</span>
                </button>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </aside>

    <nav class="fixed bottom-0 left-0 bg-slate-600 shadow-lg lg:hidden z-30 w-screen h-14">
      <ul class="flex justify-around h-full">
        <li v-for="item in navItems" :key="item.path" class="flex-1 flex">
          <NuxtLinkLocale
            :to="item.path"
            class="flex flex-col items-center justify-center p-1 text-white w-full h-full"
            :class="{ 'bg-slate-500 rounded-t-md': route.path.startsWith(item.path) }"
          >
            <component :is="item.icon" class="w-6 h-6 mb-0.5" />
          </NuxtLinkLocale>
        </li>
        <li ref="mobileSettingsRef" class="flex-1 flex relative">
          <button
            type="button"
            class="flex flex-col items-center justify-center p-1 w-full h-full text-white"
            aria-haspopup="true"
            :aria-expanded="showMobileSettingsMenu"
            @click="showMobileSettingsMenu = !showMobileSettingsMenu"
          >
            <Settings01Icon class="w-6 h-6 mb-0.5 text-white" />
          </button>

          <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showMobileSettingsMenu"
              class="absolute bottom-full right-0 mb-2 mr-2 w-56 origin-bottom-right rounded-md bg-slate-100shadow-lg z-40"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="mobile-settings-button"
              @click.stop
            >
              <div class="py-1 px-1 bg-slate-700 rounded-md" role="none">
                <div class="px-3 py-2" role="none">
                  <NavLanguageSwitcher />
                </div>
                <div class="border-t border-slate-white mx-1 my-1" role="none" />
                <button
                  type="button"
                  class="flex items-center w-full px-3 py-2 rounded-md hover:bg-pie-800 gap-2"
                  role="menuitem"
                  @click="handleLogout"
                >
                  <Logout03Icon class="w-4 h-4 stroke-2 text-white" />
                  <span class="text-sm-normal text-white">{{ t('nav.organiser.logout') }}</span>
                </button>
              </div>
            </div>
          </transition>
        </li>
      </ul>
    </nav>

    <!-- Main Content Area -->
    <main class="lg:flex-1 pt-10 pb-14 lg:pt-0 lg:pb-0">
      <div class="lg:px-4 lg:bg-pie-25 lg:min-h-screen lg:max-w-[105rem] lg:mx-auto w-max-screen">
        <slot />
      </div>
    </main>
  </div>
</template>
