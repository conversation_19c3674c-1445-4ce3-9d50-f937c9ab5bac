import NexModel from '@/models/NexModel'
import NexOrganiser from '@/models/NexOrganiser'
import NexTag from '@/models/NexTag'
import NexTicketType from '@/models/NexTicketType'
import { formatDateToDate, formatDateToTime } from '@/utils/dateUtils'
import { Deserializer } from '@/utils/deserialiser'

// const { t } = useI18n()

interface NexPolicy {
  type: string
  details: string
}

interface NexSocialMediaLink {
  link: string
  platform: string
}

interface NexPhoto {
  url: string
  key: string
}

export default class NexEvent extends NexModel {
  constructor(
    id: number,
    public name: string,
    public description: string,
    public start_time: Date,
    public end_time: Date,
    public organiser_id: number,
    public venue_name: string,
    public venue_address: string,
    public main_photo: NexPhoto,
    public latitude: number,
    public longitude: number,
    public city: string,
    public currency: string,
    public timezone: string,
    public saved: boolean,
    public tags?: NexTag[] | [],
    public social_media_links?: NexSocialMediaLink[],
    public policies?: NexPolicy[],
    public photo_gallery?: NexPhoto[] | [],
    public ticket_types?: NexTicketType[] | Array<number>,
    public organiser?: NexOrganiser,
    public disabled?: boolean,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexEvent | Array<NexEvent> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexEvent.create_instances(data)

    return NexEvent.create_instance(data)
  }

  public static formatDate(start_time: Date): string {
    return `${formatDateToDate(start_time)}, ${formatDateToTime(start_time)}`
  }

  private static create_instance(event: any): NexEvent {
    const tags = event.tags?.length > 0 && event.tags[0]?.id ? NexTag.create_instances(event.tags) : event.tags
    const ticket_types = event.ticket_types?.length > 0 && event.ticket_types[0]?.id ? NexTicketType.create_instances(event.ticket_types) : event.ticket_types

    return new NexEvent(
      event.id,
      event.name,
      event.description,
      NexModel.convertToDate(event.start_time),
      NexModel.convertToDate(event.end_time),
      event.organiser_id,
      event.venue_name,
      event.venue_address,
      event.main_photo,
      event.latitude,
      event.longitude,
      event.city,
      event.currency,
      event.timezone,
      event.saved || false,
      tags,
      event.social_media_links,
      event.policies,
      event.photo_gallery,
      ticket_types,
      event.organiser ? NexOrganiser.create_instance(event.organiser) : undefined,
      event.disabled,
    )
  }

  private static create_instances(events: Array<any>): Array<NexEvent> {
    return events.map(event => NexEvent.create_instance(event))
  }

  get dateFromTo(): string {
    if (this.start_time.toDateString() === this.end_time.toDateString()) {
      return formatDateToDate(this.start_time, this.timezone)
    }
    return `${formatDateToDate(this.start_time, this.timezone)} - ${formatDateToDate(this.end_time, this.timezone)}`
  }

  get timeFromTo(): string {
    if (this.start_time.toTimeString() === this.end_time.toTimeString()) {
      return formatDateToTime(this.start_time, this.timezone)
    }
    return `${formatDateToTime(this.start_time, this.timezone)} - ${formatDateToTime(this.end_time, this.timezone)}`
  }
}

export { }
