import NexModel from '@/models/NexModel'
import NexState from '@/models/NexStates'
import { Deserializer } from '@/utils/deserialiser'

export default class NexCity extends NexModel {
  constructor(
    id: number,
    public name: string,
    public basic_relevance: number,
    public display_name: string,
    public state: NexState,
    public timezone: string,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexCity | Array<NexCity> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any
    if (Array.isArray(data))
      return NexCity.create_instances(data)

    return NexCity.create_instance(data)
  }

  static create_instance(city: any): NexCity {
    const state = NexState.create_instance(city.state)
    return new NexCity(
      city.id,
      city.name,
      city.basic_relevance,
      city.display_name,
      state,
      city.timezone.name,
    )
  }

  static create_instances(states: Array<any>): Array<NexCity> {
    return states.map(state => NexCity.create_instance(state))
  }
}
