import NexModel from '@/models/NexModel'

interface NexPhoto {
  url: string
  key: string
}
export default class NexOrganiser extends NexModel {
  constructor(
    id: number,
    public name: string,
    public contact_email: string,
    public contact_mobile: string,
    public reg_number: string,
    public vat_number: string,
    public state_id: string,
    public address: string = '',
    public default_currency: string = 'EUR',
    public profile_picture: NexPhoto,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexOrganiser | Array<NexOrganiser> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexOrganiser.create_instances(data)

    return NexOrganiser.create_instance(data)
  }

  static create_instance(organiser: any): NexOrganiser {
    return new NexOrganiser(
      organiser.id,
      organiser.name,
      organiser.contact_email,
      organiser.contact_mobile,
      organiser.reg_number,
      organiser.vat_number,
      organiser.state_id,
      organiser.address,
      organiser.default_currency,
      organiser.profile_picture,
    )
  }

  static create_instances(organisers: Array<any>): Array<NexOrganiser> {
    return organisers.map(organiser => NexOrganiser.create_instance(organiser))
  }
}
