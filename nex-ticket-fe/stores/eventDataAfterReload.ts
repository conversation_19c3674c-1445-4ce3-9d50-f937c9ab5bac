import { defineStore } from 'pinia'

interface EventFormState {
  eventName: string
  eventDescription: string
  eventStartTime: string
  eventEndTime: string
  eventVenueName: string
  eventLat: number
  eventLong: number
}

const LOCAL_STORAGE_KEY_EVENT_FORM = 'my-event-form-draft'

export const useEventFormStore = defineStore('eventForm', {
  state: (): EventFormState => {
    let initialState: EventFormState = {
      eventName: '',
      eventDescription: '',
      eventStartTime: '',
      eventEndTime: '',
      eventVenueName: '',
      eventLat: 37.39094933041195,
      eventLong: 122.02503913145092,
    }

    if (typeof window !== 'undefined' && window.localStorage) {
      const storedData = localStorage.getItem(LOCAL_STORAGE_KEY_EVENT_FORM)
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData) as Partial<EventFormState>
          Object.assign(initialState, parsedData)
          if (typeof initialState.eventLat === 'string')
            initialState.eventLat = Number.parseFloat(initialState.eventLat)
          if (typeof initialState.eventLong === 'string')
            initialState.eventLong = Number.parseFloat(initialState.eventLong)
        }
        catch (e) {
          console.error('Error parsing localStorage data:', e)
          localStorage.removeItem(LOCAL_STORAGE_KEY_EVENT_FORM)
        }
      }
    }
    return initialState
  },

  actions: {
    setField<K extends keyof EventFormState>(field: K, value: EventFormState[K]) {
      this.$patch({
        [field]: value,
      } as Partial<EventFormState>)

      this.saveState()
    },
    resetForm() {
      this.eventName = ''
      this.eventDescription = ''
      this.eventStartTime = ''
      this.eventEndTime = ''
      this.eventVenueName = ''
      this.eventLat = 37.39094933041195
      this.eventLong = -122.02503913145092
      this.saveState()
    },
    saveState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          localStorage.setItem(LOCAL_STORAGE_KEY_EVENT_FORM, JSON.stringify(this.$state))
        }
        catch (e) {
          console.error('Error saving state to localStorage:', e)
        }
      }
    },
  },
})
