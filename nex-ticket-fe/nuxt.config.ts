// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: false },
  ssr: false,

  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@vee-validate/nuxt',
    '@nuxtjs/i18n',
    '@nuxt/scripts',
    '@nuxt/eslint',
    'vuetify-nuxt-module',
    '@vueuse/nuxt',
  ],
  app: {
    head: {
      // TODO: Dont import on dev env
      link: [{ rel: 'icon', href: 'favicon-uni.png' }],
      script: [
        {
          'src': 'https://web.cmp.usercentrics.eu/ui/loader.js',
          'id': 'usercentrics-cmp',
          'data-settings-id': 'b6pyIesNKzlc0N',
          'async': true,
        },
      ],
    },
  },

  vite: {
    server: {
      watch: {
        usePolling: true,
        interval: 100,
      },
    },
  },

  build: {
    // vue-toastification - old commonjs module
    transpile: ['vue-toastification', '@vuepic/vue-datepicker', 'vue-slider-component'],
  },

  runtimeConfig: {
    apiBaseUrl: 'rails_api:3000',
    public: {
      apiUrl: '/api',
      googleMapsApiKey: '',
      environment: 'development',
      rollbarToken: '',
      stripeKey: '',
    },
  },

  css: [
    '@/assets/vuetify/main.scss',
  ],

  vuetify: {
    moduleOptions: {
      prefixComposables: true,
      disableVuetifyStyles: true,
      styles: {
        configFile: 'assets/vuetify/settings.scss',
      },
    },
  },

  i18n: {
    locales: [
      {
        code: 'en',
        name: 'English',
        files: ['en.ts', 'promo_codes/en.ts', 'common/en.ts', 'errors/en.ts', 'footer/en.ts', 'info/en.ts', 'nav/en.ts', 'auth/en.ts', 'organiser/en.ts', 'public/en.ts', 'landing/en.ts', 'faq/en.ts', 'blog/en.ts'],
      },
      {
        code: 'sk',
        name: 'Slovak',
        files: ['sk.ts', 'promo_codes/sk.ts', 'common/sk.ts', 'errors/sk.ts', 'footer/sk.ts', 'info/sk.ts', 'nav/sk.ts', 'auth/sk.ts', 'organiser/sk.ts', 'public/sk.ts', 'landing/sk.ts', 'faq/sk.ts', 'blog/sk.ts'],
      },
      {
        code: 'de',
        name: 'German',
        files: ['de.ts', 'promo_codes/de.ts', 'common/de.ts', 'errors/de.ts', 'footer/de.ts', 'info/de.ts', 'nav/de.ts', 'auth/de.ts', 'organiser/de.ts', 'public/de.ts', 'landing/de.ts', 'faq/de.ts', 'blog/de.ts'],
      },
      {
        code: 'pl',
        name: 'Polish',
        files: ['pl.ts', 'promo_codes/pl.ts', 'common/pl.ts', 'errors/pl.ts', 'footer/pl.ts', 'info/pl.ts', 'nav/pl.ts', 'auth/pl.ts', 'organiser/pl.ts', 'public/pl.ts', 'landing/pl.ts', 'faq/pl.ts', 'blog/pl.ts'],
      },
    ],
    defaultLocale: 'en',
    strategy: 'prefix_except_default',
    langDir: 'locales/',
    lazy: true,
    bundle: {
      optimizeTranslationDirective: false,
    },
  },
})
